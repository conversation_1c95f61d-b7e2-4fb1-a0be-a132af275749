//@version=5
indicator("Custom Horizontal Lines [TDO/TWO/TMO Anchored]", overlay=true, max_lines_count=500, max_labels_count=500)

// ===== INPUT SETTINGS =====
// General Settings
cl_group_settings = "General Settings"
cl_timezone = input.string("America/New_York", "Time Zone", options=['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], group=cl_group_settings)
cl_week_start_day = input.string("Sunday", "Week Start Day", options=["Sunday", "Monday"], group=cl_group_settings)

// Line Settings
cl_group_line = "Line Settings"
cl_line_width = input.int(1, "Line Width", minval=1, maxval=5, group=cl_group_line)
cl_line_style = input.string("Solid", "Line Style", options=["Solid", "Dotted", "Dashed"], group=cl_group_line)

// Label Settings
cl_group_label = "Label Settings"
cl_show_labels = input.bool(true, "Show Labels", group=cl_group_label)
cl_show_price_in_label = input.bool(false, "Show Price in Labels", group=cl_group_label)
cl_label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=cl_group_label)
cl_label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=cl_group_label)
cl_label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=cl_group_label)

// ===== TIMEFRAME SETTINGS =====
// Anchor Selection
cl_group_anchor = "Anchor Selection"
cl_anchor_type = input.string("TMO", "Anchor Type", options=["TDO", "TWO", "TMO"], tooltip="Select which timeframe to anchor all lines to:\nTDO = True Day Open (daily)\nTWO = True Week Open (weekly)\nTMO = True Month Open (monthly)", group=cl_group_anchor)

// ===== CUSTOM HORIZONTAL LINES INPUTS =====
// Custom Lines (anchored to selected timeframe)
cl_group_lines = "Custom Lines"
cl_line1_enabled = input.bool(false, "Line 1", inline="line1", group=cl_group_lines)
cl_line1_price = input.float(0.0, "Price", inline="line1", group=cl_group_lines)
cl_line1_color = input.color(color.white, "Color", inline="line1", group=cl_group_lines)
cl_line1_label = input.string("Line 1", "Label", inline="line1", group=cl_group_lines)

cl_line2_enabled = input.bool(false, "Line 2", inline="line2", group=cl_group_lines)
cl_line2_price = input.float(0.0, "Price", inline="line2", group=cl_group_lines)
cl_line2_color = input.color(color.white, "Color", inline="line2", group=cl_group_lines)
cl_line2_label = input.string("Line 2", "Label", inline="line2", group=cl_group_lines)

cl_line3_enabled = input.bool(false, "Line 3", inline="line3", group=cl_group_lines)
cl_line3_price = input.float(0.0, "Price", inline="line3", group=cl_group_lines)
cl_line3_color = input.color(color.white, "Color", inline="line3", group=cl_group_lines)
cl_line3_label = input.string("Line 3", "Label", inline="line3", group=cl_group_lines)

cl_line4_enabled = input.bool(false, "Line 4", inline="line4", group=cl_group_lines)
cl_line4_price = input.float(0.0, "Price", inline="line4", group=cl_group_lines)
cl_line4_color = input.color(color.white, "Color", inline="line4", group=cl_group_lines)
cl_line4_label = input.string("Line 4", "Label", inline="line4", group=cl_group_lines)

cl_line5_enabled = input.bool(false, "Line 5", inline="line5", group=cl_group_lines)
cl_line5_price = input.float(0.0, "Price", inline="line5", group=cl_group_lines)
cl_line5_color = input.color(color.white, "Color", inline="line5", group=cl_group_lines)
cl_line5_label = input.string("Line 5", "Label", inline="line5", group=cl_group_lines)

cl_line6_enabled = input.bool(false, "Line 6", inline="line6", group=cl_group_lines)
cl_line6_price = input.float(0.0, "Price", inline="line6", group=cl_group_lines)
cl_line6_color = input.color(color.white, "Color", inline="line6", group=cl_group_lines)
cl_line6_label = input.string("Line 6", "Label", inline="line6", group=cl_group_lines)

cl_line7_enabled = input.bool(false, "Line 7", inline="line7", group=cl_group_lines)
cl_line7_price = input.float(0.0, "Price", inline="line7", group=cl_group_lines)
cl_line7_color = input.color(color.white, "Color", inline="line7", group=cl_group_lines)
cl_line7_label = input.string("Line 7", "Label", inline="line7", group=cl_group_lines)

cl_line8_enabled = input.bool(false, "Line 8", inline="line8", group=cl_group_lines)
cl_line8_price = input.float(0.0, "Price", inline="line8", group=cl_group_lines)
cl_line8_color = input.color(color.white, "Color", inline="line8", group=cl_group_lines)
cl_line8_label = input.string("Line 8", "Label", inline="line8", group=cl_group_lines)

cl_line9_enabled = input.bool(false, "Line 9", inline="line9", group=cl_group_lines)
cl_line9_price = input.float(0.0, "Price", inline="line9", group=cl_group_lines)
cl_line9_color = input.color(color.white, "Color", inline="line9", group=cl_group_lines)
cl_line9_label = input.string("Line 9", "Label", inline="line9", group=cl_group_lines)

cl_line10_enabled = input.bool(false, "Line 10", inline="line10", group=cl_group_lines)
cl_line10_price = input.float(0.0, "Price", inline="line10", group=cl_group_lines)
cl_line10_color = input.color(color.white, "Color", inline="line10", group=cl_group_lines)
cl_line10_label = input.string("Line 10", "Label", inline="line10", group=cl_group_lines)

cl_line11_enabled = input.bool(false, "Line 11", inline="line11", group=cl_group_lines)
cl_line11_price = input.float(0.0, "Price", inline="line11", group=cl_group_lines)
cl_line11_color = input.color(color.white, "Color", inline="line11", group=cl_group_lines)
cl_line11_label = input.string("Line 11", "Label", inline="line11", group=cl_group_lines)

cl_line12_enabled = input.bool(false, "Line 12", inline="line12", group=cl_group_lines)
cl_line12_price = input.float(0.0, "Price", inline="line12", group=cl_group_lines)
cl_line12_color = input.color(color.white, "Color", inline="line12", group=cl_group_lines)
cl_line12_label = input.string("Line 12", "Label", inline="line12", group=cl_group_lines)

cl_line13_enabled = input.bool(false, "Line 13", inline="line13", group=cl_group_lines)
cl_line13_price = input.float(0.0, "Price", inline="line13", group=cl_group_lines)
cl_line13_color = input.color(color.white, "Color", inline="line13", group=cl_group_lines)
cl_line13_label = input.string("Line 13", "Label", inline="line13", group=cl_group_lines)

cl_line14_enabled = input.bool(false, "Line 14", inline="line14", group=cl_group_lines)
cl_line14_price = input.float(0.0, "Price", inline="line14", group=cl_group_lines)
cl_line14_color = input.color(color.white, "Color", inline="line14", group=cl_group_lines)
cl_line14_label = input.string("Line 14", "Label", inline="line14", group=cl_group_lines)

cl_line15_enabled = input.bool(false, "Line 15", inline="line15", group=cl_group_lines)
cl_line15_price = input.float(0.0, "Price", inline="line15", group=cl_group_lines)
cl_line15_color = input.color(color.white, "Color", inline="line15", group=cl_group_lines)
cl_line15_label = input.string("Line 15", "Label", inline="line15", group=cl_group_lines)

cl_line16_enabled = input.bool(false, "Line 16", inline="line16", group=cl_group_lines)
cl_line16_price = input.float(0.0, "Price", inline="line16", group=cl_group_lines)
cl_line16_color = input.color(color.white, "Color", inline="line16", group=cl_group_lines)
cl_line16_label = input.string("Line 16", "Label", inline="line16", group=cl_group_lines)

cl_line17_enabled = input.bool(false, "Line 17", inline="line17", group=cl_group_lines)
cl_line17_price = input.float(0.0, "Price", inline="line17", group=cl_group_lines)
cl_line17_color = input.color(color.white, "Color", inline="line17", group=cl_group_lines)
cl_line17_label = input.string("Line 17", "Label", inline="line17", group=cl_group_lines)

cl_line18_enabled = input.bool(false, "Line 18", inline="line18", group=cl_group_lines)
cl_line18_price = input.float(0.0, "Price", inline="line18", group=cl_group_lines)
cl_line18_color = input.color(color.white, "Color", inline="line18", group=cl_group_lines)
cl_line18_label = input.string("Line 18", "Label", inline="line18", group=cl_group_lines)

cl_line19_enabled = input.bool(false, "Line 19", inline="line19", group=cl_group_lines)
cl_line19_price = input.float(0.0, "Price", inline="line19", group=cl_group_lines)
cl_line19_color = input.color(color.white, "Color", inline="line19", group=cl_group_lines)
cl_line19_label = input.string("Line 19", "Label", inline="line19", group=cl_group_lines)

cl_line20_enabled = input.bool(false, "Line 20", inline="line20", group=cl_group_lines)
cl_line20_price = input.float(0.0, "Price", inline="line20", group=cl_group_lines)
cl_line20_color = input.color(color.white, "Color", inline="line20", group=cl_group_lines)
cl_line20_label = input.string("Line 20", "Label", inline="line20", group=cl_group_lines)




// ===== HELPER FUNCTIONS =====
cl_get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

cl_get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
    result

// ===== TIME LOGIC =====
// True Day Open logic (ICT methodology - 00:00 UTC each day)
cl_is_true_day_start = (hour == 0) and (minute == 0)

// True Week Open logic (ICT methodology - 00:00 UTC on Sunday/Monday)
cl_is_true_week_start = ((cl_week_start_day == "Monday" and dayofweek == dayofweek.monday) or
                        (cl_week_start_day == "Sunday" and dayofweek == dayofweek.sunday)) and
                        (hour == 0) and (minute == 0)

// True Month Open logic (ICT methodology - 00:00 UTC on 1st of month)
cl_is_true_month_start = (dayofmonth == 1) and (hour == 0) and (minute == 0)

// ===== STATE VARIABLES =====
var cl_line_style_value = cl_get_line_style(cl_line_style)
var cl_label_size_value = cl_get_label_size(cl_label_size)

// ===== CUSTOM LINE VARIABLES =====
// TDO line variables
var line cl_tdo_line1_current = na
var line cl_tdo_line2_current = na
var line cl_tdo_line3_current = na
var line cl_tdo_line4_current = na
var line cl_tdo_line5_current = na
var line cl_tdo_line6_current = na
var line cl_tdo_line7_current = na
var line cl_tdo_line8_current = na
var line cl_tdo_line9_current = na
var line cl_tdo_line10_current = na

// TDO line labels
var label cl_tdo_line1_label_obj = na
var label cl_tdo_line2_label_obj = na
var label cl_tdo_line3_label_obj = na
var label cl_tdo_line4_label_obj = na
var label cl_tdo_line5_label_obj = na
var label cl_tdo_line6_label_obj = na
var label cl_tdo_line7_label_obj = na
var label cl_tdo_line8_label_obj = na
var label cl_tdo_line9_label_obj = na
var label cl_tdo_line10_label_obj = na

// TWO line variables
var line cl_two_line1_current = na
var line cl_two_line2_current = na
var line cl_two_line3_current = na
var line cl_two_line4_current = na
var line cl_two_line5_current = na
var line cl_two_line6_current = na
var line cl_two_line7_current = na
var line cl_two_line8_current = na
var line cl_two_line9_current = na
var line cl_two_line10_current = na

// TWO line labels
var label cl_two_line1_label_obj = na
var label cl_two_line2_label_obj = na
var label cl_two_line3_label_obj = na
var label cl_two_line4_label_obj = na
var label cl_two_line5_label_obj = na
var label cl_two_line6_label_obj = na
var label cl_two_line7_label_obj = na
var label cl_two_line8_label_obj = na
var label cl_two_line9_label_obj = na
var label cl_two_line10_label_obj = na

// TMO line variables
var line cl_tmo_line1_current = na
var line cl_tmo_line2_current = na
var line cl_tmo_line3_current = na
var line cl_tmo_line4_current = na
var line cl_tmo_line5_current = na
var line cl_tmo_line6_current = na
var line cl_tmo_line7_current = na
var line cl_tmo_line8_current = na
var line cl_tmo_line9_current = na
var line cl_tmo_line10_current = na

// TMO line labels
var label cl_tmo_line1_label_obj = na
var label cl_tmo_line2_label_obj = na
var label cl_tmo_line3_label_obj = na
var label cl_tmo_line4_label_obj = na
var label cl_tmo_line5_label_obj = na
var label cl_tmo_line6_label_obj = na
var label cl_tmo_line7_label_obj = na
var label cl_tmo_line8_label_obj = na
var label cl_tmo_line9_label_obj = na
var label cl_tmo_line10_label_obj = na

// Anchor tracking
var bool cl_tdo_line_active = false
var bool cl_two_line_active = false
var bool cl_tmo_line_active = false

// ===== MAIN LOGIC =====
// Check if we're at the start of a new day (TDO)
if cl_is_true_day_start and not cl_is_true_day_start[1] and cl_show_tdo_lines
    cl_tdo_line_active := true

    // Create TDO custom lines
    if cl_tdo_line1_enabled and cl_tdo_line1_price != 0.0
        cl_tdo_line1_current := line.new(bar_index, cl_tdo_line1_price, bar_index, cl_tdo_line1_price, color=cl_tdo_line1_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line1_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line1_price) : "")
            cl_tdo_line1_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line1_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line1_color, color=color.new(color.black, 100))

    if cl_tdo_line2_enabled and cl_tdo_line2_price != 0.0
        cl_tdo_line2_current := line.new(bar_index, cl_tdo_line2_price, bar_index, cl_tdo_line2_price, color=cl_tdo_line2_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line2_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line2_price) : "")
            cl_tdo_line2_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line2_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line2_color, color=color.new(color.black, 100))

    if cl_tdo_line3_enabled and cl_tdo_line3_price != 0.0
        cl_tdo_line3_current := line.new(bar_index, cl_tdo_line3_price, bar_index, cl_tdo_line3_price, color=cl_tdo_line3_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line3_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line3_price) : "")
            cl_tdo_line3_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line3_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line3_color, color=color.new(color.black, 100))

    if cl_tdo_line4_enabled and cl_tdo_line4_price != 0.0
        cl_tdo_line4_current := line.new(bar_index, cl_tdo_line4_price, bar_index, cl_tdo_line4_price, color=cl_tdo_line4_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line4_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line4_price) : "")
            cl_tdo_line4_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line4_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line4_color, color=color.new(color.black, 100))

    if cl_tdo_line5_enabled and cl_tdo_line5_price != 0.0
        cl_tdo_line5_current := line.new(bar_index, cl_tdo_line5_price, bar_index, cl_tdo_line5_price, color=cl_tdo_line5_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line5_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line5_price) : "")
            cl_tdo_line5_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line5_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line5_color, color=color.new(color.black, 100))

    if cl_tdo_line6_enabled and cl_tdo_line6_price != 0.0
        cl_tdo_line6_current := line.new(bar_index, cl_tdo_line6_price, bar_index, cl_tdo_line6_price, color=cl_tdo_line6_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line6_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line6_price) : "")
            cl_tdo_line6_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line6_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line6_color, color=color.new(color.black, 100))

    if cl_tdo_line7_enabled and cl_tdo_line7_price != 0.0
        cl_tdo_line7_current := line.new(bar_index, cl_tdo_line7_price, bar_index, cl_tdo_line7_price, color=cl_tdo_line7_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line7_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line7_price) : "")
            cl_tdo_line7_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line7_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line7_color, color=color.new(color.black, 100))

    if cl_tdo_line8_enabled and cl_tdo_line8_price != 0.0
        cl_tdo_line8_current := line.new(bar_index, cl_tdo_line8_price, bar_index, cl_tdo_line8_price, color=cl_tdo_line8_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line8_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line8_price) : "")
            cl_tdo_line8_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line8_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line8_color, color=color.new(color.black, 100))

    if cl_tdo_line9_enabled and cl_tdo_line9_price != 0.0
        cl_tdo_line9_current := line.new(bar_index, cl_tdo_line9_price, bar_index, cl_tdo_line9_price, color=cl_tdo_line9_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line9_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line9_price) : "")
            cl_tdo_line9_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line9_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line9_color, color=color.new(color.black, 100))

    if cl_tdo_line10_enabled and cl_tdo_line10_price != 0.0
        cl_tdo_line10_current := line.new(bar_index, cl_tdo_line10_price, bar_index, cl_tdo_line10_price, color=cl_tdo_line10_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tdo_line10_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tdo_line10_price) : "")
            cl_tdo_line10_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tdo_line10_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tdo_line10_color, color=color.new(color.black, 100))

// Check if we're at the start of a new week (TWO)
if cl_is_true_week_start and not cl_is_true_week_start[1] and cl_show_two_lines
    cl_two_line_active := true

    // Create TWO custom lines
    if cl_two_line1_enabled and cl_two_line1_price != 0.0
        cl_two_line1_current := line.new(bar_index, cl_two_line1_price, bar_index, cl_two_line1_price, color=cl_two_line1_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line1_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line1_price) : "")
            cl_two_line1_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line1_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line1_color, color=color.new(color.black, 100))

    if cl_two_line2_enabled and cl_two_line2_price != 0.0
        cl_two_line2_current := line.new(bar_index, cl_two_line2_price, bar_index, cl_two_line2_price, color=cl_two_line2_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line2_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line2_price) : "")
            cl_two_line2_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line2_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line2_color, color=color.new(color.black, 100))

    if cl_two_line3_enabled and cl_two_line3_price != 0.0
        cl_two_line3_current := line.new(bar_index, cl_two_line3_price, bar_index, cl_two_line3_price, color=cl_two_line3_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line3_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line3_price) : "")
            cl_two_line3_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line3_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line3_color, color=color.new(color.black, 100))

    if cl_two_line4_enabled and cl_two_line4_price != 0.0
        cl_two_line4_current := line.new(bar_index, cl_two_line4_price, bar_index, cl_two_line4_price, color=cl_two_line4_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line4_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line4_price) : "")
            cl_two_line4_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line4_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line4_color, color=color.new(color.black, 100))

    if cl_two_line5_enabled and cl_two_line5_price != 0.0
        cl_two_line5_current := line.new(bar_index, cl_two_line5_price, bar_index, cl_two_line5_price, color=cl_two_line5_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line5_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line5_price) : "")
            cl_two_line5_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line5_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line5_color, color=color.new(color.black, 100))

    if cl_two_line6_enabled and cl_two_line6_price != 0.0
        cl_two_line6_current := line.new(bar_index, cl_two_line6_price, bar_index, cl_two_line6_price, color=cl_two_line6_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line6_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line6_price) : "")
            cl_two_line6_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line6_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line6_color, color=color.new(color.black, 100))

    if cl_two_line7_enabled and cl_two_line7_price != 0.0
        cl_two_line7_current := line.new(bar_index, cl_two_line7_price, bar_index, cl_two_line7_price, color=cl_two_line7_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line7_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line7_price) : "")
            cl_two_line7_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line7_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line7_color, color=color.new(color.black, 100))

    if cl_two_line8_enabled and cl_two_line8_price != 0.0
        cl_two_line8_current := line.new(bar_index, cl_two_line8_price, bar_index, cl_two_line8_price, color=cl_two_line8_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line8_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line8_price) : "")
            cl_two_line8_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line8_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line8_color, color=color.new(color.black, 100))

    if cl_two_line9_enabled and cl_two_line9_price != 0.0
        cl_two_line9_current := line.new(bar_index, cl_two_line9_price, bar_index, cl_two_line9_price, color=cl_two_line9_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line9_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line9_price) : "")
            cl_two_line9_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line9_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line9_color, color=color.new(color.black, 100))

    if cl_two_line10_enabled and cl_two_line10_price != 0.0
        cl_two_line10_current := line.new(bar_index, cl_two_line10_price, bar_index, cl_two_line10_price, color=cl_two_line10_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_two_line10_label + (cl_show_price_in_label ? str.format(" ({0})", cl_two_line10_price) : "")
            cl_two_line10_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_two_line10_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_two_line10_color, color=color.new(color.black, 100))

// Check if we're at the start of a new month (TMO)
if cl_is_true_month_start and not cl_is_true_month_start[1] and cl_show_tmo_lines
    cl_tmo_line_active := true

    // Create TMO custom lines
    if cl_tmo_line1_enabled and cl_tmo_line1_price != 0.0
        cl_tmo_line1_current := line.new(bar_index, cl_tmo_line1_price, bar_index, cl_tmo_line1_price, color=cl_tmo_line1_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line1_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line1_price) : "")
            cl_tmo_line1_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line1_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line1_color, color=color.new(color.black, 100))

    if cl_tmo_line2_enabled and cl_tmo_line2_price != 0.0
        cl_tmo_line2_current := line.new(bar_index, cl_tmo_line2_price, bar_index, cl_tmo_line2_price, color=cl_tmo_line2_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line2_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line2_price) : "")
            cl_tmo_line2_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line2_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line2_color, color=color.new(color.black, 100))

    if cl_tmo_line3_enabled and cl_tmo_line3_price != 0.0
        cl_tmo_line3_current := line.new(bar_index, cl_tmo_line3_price, bar_index, cl_tmo_line3_price, color=cl_tmo_line3_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line3_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line3_price) : "")
            cl_tmo_line3_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line3_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line3_color, color=color.new(color.black, 100))

    if cl_tmo_line4_enabled and cl_tmo_line4_price != 0.0
        cl_tmo_line4_current := line.new(bar_index, cl_tmo_line4_price, bar_index, cl_tmo_line4_price, color=cl_tmo_line4_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line4_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line4_price) : "")
            cl_tmo_line4_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line4_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line4_color, color=color.new(color.black, 100))

    if cl_tmo_line5_enabled and cl_tmo_line5_price != 0.0
        cl_tmo_line5_current := line.new(bar_index, cl_tmo_line5_price, bar_index, cl_tmo_line5_price, color=cl_tmo_line5_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line5_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line5_price) : "")
            cl_tmo_line5_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line5_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line5_color, color=color.new(color.black, 100))

    if cl_tmo_line6_enabled and cl_tmo_line6_price != 0.0
        cl_tmo_line6_current := line.new(bar_index, cl_tmo_line6_price, bar_index, cl_tmo_line6_price, color=cl_tmo_line6_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line6_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line6_price) : "")
            cl_tmo_line6_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line6_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line6_color, color=color.new(color.black, 100))

    if cl_tmo_line7_enabled and cl_tmo_line7_price != 0.0
        cl_tmo_line7_current := line.new(bar_index, cl_tmo_line7_price, bar_index, cl_tmo_line7_price, color=cl_tmo_line7_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line7_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line7_price) : "")
            cl_tmo_line7_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line7_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line7_color, color=color.new(color.black, 100))

    if cl_tmo_line8_enabled and cl_tmo_line8_price != 0.0
        cl_tmo_line8_current := line.new(bar_index, cl_tmo_line8_price, bar_index, cl_tmo_line8_price, color=cl_tmo_line8_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line8_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line8_price) : "")
            cl_tmo_line8_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line8_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line8_color, color=color.new(color.black, 100))

    if cl_tmo_line9_enabled and cl_tmo_line9_price != 0.0
        cl_tmo_line9_current := line.new(bar_index, cl_tmo_line9_price, bar_index, cl_tmo_line9_price, color=cl_tmo_line9_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line9_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line9_price) : "")
            cl_tmo_line9_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line9_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line9_color, color=color.new(color.black, 100))

    if cl_tmo_line10_enabled and cl_tmo_line10_price != 0.0
        cl_tmo_line10_current := line.new(bar_index, cl_tmo_line10_price, bar_index, cl_tmo_line10_price, color=cl_tmo_line10_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_tmo_line10_label + (cl_show_price_in_label ? str.format(" ({0})", cl_tmo_line10_price) : "")
            cl_tmo_line10_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_tmo_line10_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_tmo_line10_color, color=color.new(color.black, 100))

// ===== LINE EXTENSION LOGIC =====
// Continuously extend TDO lines to current bar when TDO line is active
if cl_tdo_line_active and cl_show_tdo_lines
    // Update TDO custom lines
    if cl_tdo_line1_enabled and cl_tdo_line1_price != 0.0 and not na(cl_tdo_line1_current)
        line.set_x2(cl_tdo_line1_current, bar_index)
        line.set_y2(cl_tdo_line1_current, cl_tdo_line1_price)
        if cl_show_labels and not na(cl_tdo_line1_label_obj)
            label.set_x(cl_tdo_line1_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line1_label_obj, cl_tdo_line1_price + cl_label_y_offset)

    if cl_tdo_line2_enabled and cl_tdo_line2_price != 0.0 and not na(cl_tdo_line2_current)
        line.set_x2(cl_tdo_line2_current, bar_index)
        line.set_y2(cl_tdo_line2_current, cl_tdo_line2_price)
        if cl_show_labels and not na(cl_tdo_line2_label_obj)
            label.set_x(cl_tdo_line2_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line2_label_obj, cl_tdo_line2_price + cl_label_y_offset)

    if cl_tdo_line3_enabled and cl_tdo_line3_price != 0.0 and not na(cl_tdo_line3_current)
        line.set_x2(cl_tdo_line3_current, bar_index)
        line.set_y2(cl_tdo_line3_current, cl_tdo_line3_price)
        if cl_show_labels and not na(cl_tdo_line3_label_obj)
            label.set_x(cl_tdo_line3_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line3_label_obj, cl_tdo_line3_price + cl_label_y_offset)

    if cl_tdo_line4_enabled and cl_tdo_line4_price != 0.0 and not na(cl_tdo_line4_current)
        line.set_x2(cl_tdo_line4_current, bar_index)
        line.set_y2(cl_tdo_line4_current, cl_tdo_line4_price)
        if cl_show_labels and not na(cl_tdo_line4_label_obj)
            label.set_x(cl_tdo_line4_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line4_label_obj, cl_tdo_line4_price + cl_label_y_offset)

    if cl_tdo_line5_enabled and cl_tdo_line5_price != 0.0 and not na(cl_tdo_line5_current)
        line.set_x2(cl_tdo_line5_current, bar_index)
        line.set_y2(cl_tdo_line5_current, cl_tdo_line5_price)
        if cl_show_labels and not na(cl_tdo_line5_label_obj)
            label.set_x(cl_tdo_line5_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line5_label_obj, cl_tdo_line5_price + cl_label_y_offset)

    if cl_tdo_line6_enabled and cl_tdo_line6_price != 0.0 and not na(cl_tdo_line6_current)
        line.set_x2(cl_tdo_line6_current, bar_index)
        line.set_y2(cl_tdo_line6_current, cl_tdo_line6_price)
        if cl_show_labels and not na(cl_tdo_line6_label_obj)
            label.set_x(cl_tdo_line6_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line6_label_obj, cl_tdo_line6_price + cl_label_y_offset)

    if cl_tdo_line7_enabled and cl_tdo_line7_price != 0.0 and not na(cl_tdo_line7_current)
        line.set_x2(cl_tdo_line7_current, bar_index)
        line.set_y2(cl_tdo_line7_current, cl_tdo_line7_price)
        if cl_show_labels and not na(cl_tdo_line7_label_obj)
            label.set_x(cl_tdo_line7_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line7_label_obj, cl_tdo_line7_price + cl_label_y_offset)

    if cl_tdo_line8_enabled and cl_tdo_line8_price != 0.0 and not na(cl_tdo_line8_current)
        line.set_x2(cl_tdo_line8_current, bar_index)
        line.set_y2(cl_tdo_line8_current, cl_tdo_line8_price)
        if cl_show_labels and not na(cl_tdo_line8_label_obj)
            label.set_x(cl_tdo_line8_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line8_label_obj, cl_tdo_line8_price + cl_label_y_offset)

    if cl_tdo_line9_enabled and cl_tdo_line9_price != 0.0 and not na(cl_tdo_line9_current)
        line.set_x2(cl_tdo_line9_current, bar_index)
        line.set_y2(cl_tdo_line9_current, cl_tdo_line9_price)
        if cl_show_labels and not na(cl_tdo_line9_label_obj)
            label.set_x(cl_tdo_line9_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line9_label_obj, cl_tdo_line9_price + cl_label_y_offset)

    if cl_tdo_line10_enabled and cl_tdo_line10_price != 0.0 and not na(cl_tdo_line10_current)
        line.set_x2(cl_tdo_line10_current, bar_index)
        line.set_y2(cl_tdo_line10_current, cl_tdo_line10_price)
        if cl_show_labels and not na(cl_tdo_line10_label_obj)
            label.set_x(cl_tdo_line10_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tdo_line10_label_obj, cl_tdo_line10_price + cl_label_y_offset)

// Continuously extend TWO lines to current bar when TWO line is active
if cl_two_line_active and cl_show_two_lines
    // Update TWO custom lines
    if cl_two_line1_enabled and cl_two_line1_price != 0.0 and not na(cl_two_line1_current)
        line.set_x2(cl_two_line1_current, bar_index)
        line.set_y2(cl_two_line1_current, cl_two_line1_price)
        if cl_show_labels and not na(cl_two_line1_label_obj)
            label.set_x(cl_two_line1_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line1_label_obj, cl_two_line1_price + cl_label_y_offset)

    if cl_two_line2_enabled and cl_two_line2_price != 0.0 and not na(cl_two_line2_current)
        line.set_x2(cl_two_line2_current, bar_index)
        line.set_y2(cl_two_line2_current, cl_two_line2_price)
        if cl_show_labels and not na(cl_two_line2_label_obj)
            label.set_x(cl_two_line2_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line2_label_obj, cl_two_line2_price + cl_label_y_offset)

    if cl_two_line3_enabled and cl_two_line3_price != 0.0 and not na(cl_two_line3_current)
        line.set_x2(cl_two_line3_current, bar_index)
        line.set_y2(cl_two_line3_current, cl_two_line3_price)
        if cl_show_labels and not na(cl_two_line3_label_obj)
            label.set_x(cl_two_line3_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line3_label_obj, cl_two_line3_price + cl_label_y_offset)

    if cl_two_line4_enabled and cl_two_line4_price != 0.0 and not na(cl_two_line4_current)
        line.set_x2(cl_two_line4_current, bar_index)
        line.set_y2(cl_two_line4_current, cl_two_line4_price)
        if cl_show_labels and not na(cl_two_line4_label_obj)
            label.set_x(cl_two_line4_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line4_label_obj, cl_two_line4_price + cl_label_y_offset)

    if cl_two_line5_enabled and cl_two_line5_price != 0.0 and not na(cl_two_line5_current)
        line.set_x2(cl_two_line5_current, bar_index)
        line.set_y2(cl_two_line5_current, cl_two_line5_price)
        if cl_show_labels and not na(cl_two_line5_label_obj)
            label.set_x(cl_two_line5_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line5_label_obj, cl_two_line5_price + cl_label_y_offset)

    if cl_two_line6_enabled and cl_two_line6_price != 0.0 and not na(cl_two_line6_current)
        line.set_x2(cl_two_line6_current, bar_index)
        line.set_y2(cl_two_line6_current, cl_two_line6_price)
        if cl_show_labels and not na(cl_two_line6_label_obj)
            label.set_x(cl_two_line6_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line6_label_obj, cl_two_line6_price + cl_label_y_offset)

    if cl_two_line7_enabled and cl_two_line7_price != 0.0 and not na(cl_two_line7_current)
        line.set_x2(cl_two_line7_current, bar_index)
        line.set_y2(cl_two_line7_current, cl_two_line7_price)
        if cl_show_labels and not na(cl_two_line7_label_obj)
            label.set_x(cl_two_line7_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line7_label_obj, cl_two_line7_price + cl_label_y_offset)

    if cl_two_line8_enabled and cl_two_line8_price != 0.0 and not na(cl_two_line8_current)
        line.set_x2(cl_two_line8_current, bar_index)
        line.set_y2(cl_two_line8_current, cl_two_line8_price)
        if cl_show_labels and not na(cl_two_line8_label_obj)
            label.set_x(cl_two_line8_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line8_label_obj, cl_two_line8_price + cl_label_y_offset)

    if cl_two_line9_enabled and cl_two_line9_price != 0.0 and not na(cl_two_line9_current)
        line.set_x2(cl_two_line9_current, bar_index)
        line.set_y2(cl_two_line9_current, cl_two_line9_price)
        if cl_show_labels and not na(cl_two_line9_label_obj)
            label.set_x(cl_two_line9_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line9_label_obj, cl_two_line9_price + cl_label_y_offset)

    if cl_two_line10_enabled and cl_two_line10_price != 0.0 and not na(cl_two_line10_current)
        line.set_x2(cl_two_line10_current, bar_index)
        line.set_y2(cl_two_line10_current, cl_two_line10_price)
        if cl_show_labels and not na(cl_two_line10_label_obj)
            label.set_x(cl_two_line10_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_two_line10_label_obj, cl_two_line10_price + cl_label_y_offset)

// Continuously extend TMO lines to current bar when TMO line is active
if cl_tmo_line_active and cl_show_tmo_lines
    // Update TMO custom lines
    if cl_tmo_line1_enabled and cl_tmo_line1_price != 0.0 and not na(cl_tmo_line1_current)
        line.set_x2(cl_tmo_line1_current, bar_index)
        line.set_y2(cl_tmo_line1_current, cl_tmo_line1_price)
        if cl_show_labels and not na(cl_tmo_line1_label_obj)
            label.set_x(cl_tmo_line1_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line1_label_obj, cl_tmo_line1_price + cl_label_y_offset)

    if cl_tmo_line2_enabled and cl_tmo_line2_price != 0.0 and not na(cl_tmo_line2_current)
        line.set_x2(cl_tmo_line2_current, bar_index)
        line.set_y2(cl_tmo_line2_current, cl_tmo_line2_price)
        if cl_show_labels and not na(cl_tmo_line2_label_obj)
            label.set_x(cl_tmo_line2_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line2_label_obj, cl_tmo_line2_price + cl_label_y_offset)

    if cl_tmo_line3_enabled and cl_tmo_line3_price != 0.0 and not na(cl_tmo_line3_current)
        line.set_x2(cl_tmo_line3_current, bar_index)
        line.set_y2(cl_tmo_line3_current, cl_tmo_line3_price)
        if cl_show_labels and not na(cl_tmo_line3_label_obj)
            label.set_x(cl_tmo_line3_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line3_label_obj, cl_tmo_line3_price + cl_label_y_offset)

    if cl_tmo_line4_enabled and cl_tmo_line4_price != 0.0 and not na(cl_tmo_line4_current)
        line.set_x2(cl_tmo_line4_current, bar_index)
        line.set_y2(cl_tmo_line4_current, cl_tmo_line4_price)
        if cl_show_labels and not na(cl_tmo_line4_label_obj)
            label.set_x(cl_tmo_line4_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line4_label_obj, cl_tmo_line4_price + cl_label_y_offset)

    if cl_tmo_line5_enabled and cl_tmo_line5_price != 0.0 and not na(cl_tmo_line5_current)
        line.set_x2(cl_tmo_line5_current, bar_index)
        line.set_y2(cl_tmo_line5_current, cl_tmo_line5_price)
        if cl_show_labels and not na(cl_tmo_line5_label_obj)
            label.set_x(cl_tmo_line5_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line5_label_obj, cl_tmo_line5_price + cl_label_y_offset)

    if cl_tmo_line6_enabled and cl_tmo_line6_price != 0.0 and not na(cl_tmo_line6_current)
        line.set_x2(cl_tmo_line6_current, bar_index)
        line.set_y2(cl_tmo_line6_current, cl_tmo_line6_price)
        if cl_show_labels and not na(cl_tmo_line6_label_obj)
            label.set_x(cl_tmo_line6_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line6_label_obj, cl_tmo_line6_price + cl_label_y_offset)

    if cl_tmo_line7_enabled and cl_tmo_line7_price != 0.0 and not na(cl_tmo_line7_current)
        line.set_x2(cl_tmo_line7_current, bar_index)
        line.set_y2(cl_tmo_line7_current, cl_tmo_line7_price)
        if cl_show_labels and not na(cl_tmo_line7_label_obj)
            label.set_x(cl_tmo_line7_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line7_label_obj, cl_tmo_line7_price + cl_label_y_offset)

    if cl_tmo_line8_enabled and cl_tmo_line8_price != 0.0 and not na(cl_tmo_line8_current)
        line.set_x2(cl_tmo_line8_current, bar_index)
        line.set_y2(cl_tmo_line8_current, cl_tmo_line8_price)
        if cl_show_labels and not na(cl_tmo_line8_label_obj)
            label.set_x(cl_tmo_line8_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line8_label_obj, cl_tmo_line8_price + cl_label_y_offset)

    if cl_tmo_line9_enabled and cl_tmo_line9_price != 0.0 and not na(cl_tmo_line9_current)
        line.set_x2(cl_tmo_line9_current, bar_index)
        line.set_y2(cl_tmo_line9_current, cl_tmo_line9_price)
        if cl_show_labels and not na(cl_tmo_line9_label_obj)
            label.set_x(cl_tmo_line9_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line9_label_obj, cl_tmo_line9_price + cl_label_y_offset)

    if cl_tmo_line10_enabled and cl_tmo_line10_price != 0.0 and not na(cl_tmo_line10_current)
        line.set_x2(cl_tmo_line10_current, bar_index)
        line.set_y2(cl_tmo_line10_current, cl_tmo_line10_price)
        if cl_show_labels and not na(cl_tmo_line10_label_obj)
            label.set_x(cl_tmo_line10_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_tmo_line10_label_obj, cl_tmo_line10_price + cl_label_y_offset)