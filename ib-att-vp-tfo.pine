//@version=5

// indicator("Volume Profile [TFO]", "Volume Profile [TFO]", true, max_bars_back=5000, max_lines_count = 500, max_polylines_count = 100, max_labels_count = 500, max_boxes_count = 500)
indicator("Initial Balance-Advanced Time Technique-Volume Profile", shorttitle="IB-ATT-VP TFO", overlay=true,max_bars_back=5000, max_lines_count = 500, max_polylines_count = 100, max_labels_count = 500, max_boxes_count = 500)

//----IB indicator -----
// Options
// @version=5

// === INPUTS ===
ib_session               = input.session("0820-0920", title="Calculation period for the initial balance", group="Calculation period")
show_extra_levels        = input.bool(true,  "Show extra levels (IBH x2 & IBL x2)",                 group="Information")
show_intermediate_levels = input.bool(true,  "Show intermediate levels (50%)",                      group="Information")
show_ib_calculation_area = input.bool(true,  "Initial balance calculation period coloration",      group="Information")
fill_ib_areas            = input.bool(true,  "Colour IB areas",                                    group="Information")
only_current_levels      = input.bool(false, "Only display the current IB Levels",                 group="Information")
only_current_zone        = input.bool(false, "Only display the current IB calculation area",      group="Information")

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === DRAWING PARAMETERS ===
lvl_width         = input.int(1,           "Daily price level width",                               group="Drawings")
high_col          = input.color(color.green,   "Initial balance high levels color",                group="Drawings")
low_col           = input.color(color.red,     "Initial balance low levels color",                 group="Drawings")
middle_col        = input.color(#ffa726,       "50% initial balance color",                        group="Drawings")
main_levels_style = input.string("Solid",  "Main levels line style",     options=["Solid","Dashed","Dotted"], group="Drawings")
ext_levels_style  = input.string("Dashed", "Extended levels line style", options=["Solid","Dashed","Dotted"], group="Drawings")
int_levels_style  = input.string("Dotted", "Intermediate levels line style", options=["Solid","Dashed","Dotted"], group="Drawings")
fill_ib_color     = input.color(#b8851faa, "IB area background color",                         group="Drawings")
ext = extend.none  // manage extension manually

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === UTILITIES ===
inSession(sess) => na(time(timeframe.period, sess)) == false
get_levels(n) =>
    float h = high[1]
    float l = low[1]
    for i = 1 to n
        if low[i]  < l
            l := low[i]
        if high[i] > h
            h := high[i]
    [h, l, (h + l) / 2]

// Function to manage line arrays based on max_periods
max_periods = 20
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// === STATE VARIABLES ===
var line_style_value = get_line_style(main_levels_style)
var label_size_value = get_label_size(label_size)

// === SESSION STATE ===
var float ib_delta       = na
var int   offset         = 0
var float[] delta_history = array.new_float(20)
ins = inSession(ib_session)
bgcolor(show_ib_calculation_area and ins ? #673ab730 : na, title="IB calculation zone")

// === LINE AND LABEL ARRAYS ===
var array<line> ibh_lines = array.new<line>()
var array<line> ibl_lines = array.new<line>()
var array<line> ibm_lines = array.new<line>()
var array<line> ib_plus_lines = array.new<line>()
var array<line> ib_minus_lines = array.new<line>()
var array<line> ib_plus2_lines = array.new<line>()
var array<line> ib_minus2_lines = array.new<line>()
var array<line> ibm_plus_lines = array.new<line>()
var array<line> ibm_minus_lines = array.new<line>()

var array<label> ibh_labels = array.new<label>()
var array<label> ibl_labels = array.new<label>()
var array<label> ibm_labels = array.new<label>()
var array<label> ib_plus_labels = array.new<label>()
var array<label> ib_minus_labels = array.new<label>()
var array<label> ib_plus2_labels = array.new<label>()
var array<label> ib_minus2_labels = array.new<label>()
var array<label> ibm_plus_labels = array.new<label>()
var array<label> ibm_minus_labels = array.new<label>()

// === CURRENT LINES AND LABELS ===
var line  ibh_line        = na
var line  ibl_line        = na
var line  ibm_line        = na
var line  ib_plus_line    = na
var line  ib_minus_line   = na
var line  ib_plus2_line   = na
var line  ib_minus2_line  = na
var line  ibm_plus_line   = na
var line  ibm_minus_line  = na
var box   ib_area         = na

var label ibh_label       = na
var label ibl_label       = na
var label ibm_label       = na
var label ib_plus_label   = na
var label ib_minus_label  = na
var label ib_plus2_label  = na
var label ib_minus2_label = na
var label ibm_plus_label  = na
var label ibm_minus_label = na

// === BUILD SESSION ===
if ins
    offset += 1

if ins[1] and not ins
    // calculate levels
    [h, l, m] = get_levels(offset)
    ib_delta := h - l

    // update history
    if array.size(delta_history) >= 20
        array.shift(delta_history)
    array.push(delta_history, ib_delta)

    // Delete old labels when a new session starts
    if show_labels
        delete_all_labels(ibh_labels)
        delete_all_labels(ibl_labels)
        delete_all_labels(ibm_labels)
        delete_all_labels(ib_plus_labels)
        delete_all_labels(ib_minus_labels)
        delete_all_labels(ib_plus2_labels)
        delete_all_labels(ib_minus2_labels)
        delete_all_labels(ibm_plus_labels)
        delete_all_labels(ibm_minus_labels)

    // Create main level lines
    ibh_line := line.new(bar_index, h, bar_index, h, color=high_col, width=lvl_width, style=line_style_value)
    array.unshift(ibh_lines, ibh_line)
    manage_line_history(ibh_lines)

    ibl_line := line.new(bar_index, l, bar_index, l, color=low_col, width=lvl_width, style=line_style_value)
    array.unshift(ibl_lines, ibl_line)
    manage_line_history(ibl_lines)

    // Main level labels
    if show_labels
        ibh_label := label.new(bar_index + label_x_offset_bars, h + label_y_offset, "IBH" + (show_price_in_label ? str.format(" ({0})", h) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
        array.push(ibh_labels, ibh_label)

        ibl_label := label.new(bar_index + label_x_offset_bars, l + label_y_offset, "IBL" + (show_price_in_label ? str.format(" ({0})", l) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
        array.push(ibl_labels, ibl_label)

    // intermediate 50% line
    if show_intermediate_levels
        ibm_line := line.new(bar_index, m, bar_index, m, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
        array.unshift(ibm_lines, ibm_line)
        manage_line_history(ibm_lines)

        if show_labels
            ibm_label := label.new(bar_index + label_x_offset_bars, m + label_y_offset, "IBM" + (show_price_in_label ? str.format(" ({0})", m) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
            array.push(ibm_labels, ibm_label)

    // extra levels
    if show_extra_levels
        ib_plus_line := line.new(bar_index, h + ib_delta, bar_index, h + ib_delta, color=high_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_plus_lines, ib_plus_line)
        manage_line_history(ib_plus_lines)

        ib_minus_line := line.new(bar_index, l - ib_delta, bar_index, l - ib_delta, color=low_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_minus_lines, ib_minus_line)
        manage_line_history(ib_minus_lines)

        ib_plus2_line := line.new(bar_index, h + ib_delta * 2, bar_index, h + ib_delta * 2, color=high_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_plus2_lines, ib_plus2_line)
        manage_line_history(ib_plus2_lines)

        ib_minus2_line := line.new(bar_index, l - ib_delta * 2, bar_index, l - ib_delta * 2, color=low_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_minus2_lines, ib_minus2_line)
        manage_line_history(ib_minus2_lines)

        if show_labels
            ib_plus_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta) + label_y_offset, "IBH+Δ" + (show_price_in_label ? str.format(" ({0})", h + ib_delta) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
            array.push(ib_plus_labels, ib_plus_label)

            ib_minus_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta) + label_y_offset, "IBL-Δ" + (show_price_in_label ? str.format(" ({0})", l - ib_delta) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
            array.push(ib_minus_labels, ib_minus_label)

            ib_plus2_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta * 2) + label_y_offset, "IBH+2Δ" + (show_price_in_label ? str.format(" ({0})", h + ib_delta * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
            array.push(ib_plus2_labels, ib_plus2_label)

            ib_minus2_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta * 2) + label_y_offset, "IBL-2Δ" + (show_price_in_label ? str.format(" ({0})", l - ib_delta * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
            array.push(ib_minus2_labels, ib_minus2_label)

        if show_intermediate_levels
            ibm_plus_line := line.new(bar_index, h + ib_delta / 2, bar_index, h + ib_delta / 2, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
            array.unshift(ibm_plus_lines, ibm_plus_line)
            manage_line_history(ibm_plus_lines)

            ibm_minus_line := line.new(bar_index, l - ib_delta / 2, bar_index, l - ib_delta / 2, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
            array.unshift(ibm_minus_lines, ibm_minus_line)
            manage_line_history(ibm_minus_lines)

            if show_labels
                ibm_plus_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta / 2) + label_y_offset, "IBM+Δ/2" + (show_price_in_label ? str.format(" ({0})", h + ib_delta / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
                array.push(ibm_plus_labels, ibm_plus_label)

                ibm_minus_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta / 2) + label_y_offset, "IBM-Δ/2" + (show_price_in_label ? str.format(" ({0})", l - ib_delta / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
                array.push(ibm_minus_labels, ibm_minus_label)

    // fill the IB area
    if fill_ib_areas
        ib_area := box.new(bar_index[offset], h, bar_index, l, bgcolor=fill_ib_color, border_color=color.new(color.black, 100))

    offset := 0

// === UPDATE LINES DURING THE CURRENT DAY ===
if not ins and not na(ibh_line)
    // Update main level lines
    line.set_x2(ibh_line, bar_index)
    line.set_x2(ibl_line, bar_index)

    if show_labels
        label.set_x(ibh_label, bar_index + label_x_offset_bars)
        label.set_x(ibl_label, bar_index + label_x_offset_bars)

    if show_intermediate_levels and not na(ibm_line)
        line.set_x2(ibm_line, bar_index)
        if show_labels
            label.set_x(ibm_label, bar_index + label_x_offset_bars)

    if show_extra_levels
        if not na(ib_plus_line)
            line.set_x2(ib_plus_line, bar_index)
        if not na(ib_minus_line)
            line.set_x2(ib_minus_line, bar_index)
        if not na(ib_plus2_line)
            line.set_x2(ib_plus2_line, bar_index)
        if not na(ib_minus2_line)
            line.set_x2(ib_minus2_line, bar_index)

        if show_labels
            if not na(ib_plus_label)
                label.set_x(ib_plus_label, bar_index + label_x_offset_bars)
            if not na(ib_minus_label)
                label.set_x(ib_minus_label, bar_index + label_x_offset_bars)
            if not na(ib_plus2_label)
                label.set_x(ib_plus2_label, bar_index + label_x_offset_bars)
            if not na(ib_minus2_label)
                label.set_x(ib_minus2_label, bar_index + label_x_offset_bars)

        if show_intermediate_levels
            if not na(ibm_plus_line)
                line.set_x2(ibm_plus_line, bar_index)
            if not na(ibm_minus_line)
                line.set_x2(ibm_minus_line, bar_index)

            if show_labels
                if not na(ibm_plus_label)
                    label.set_x(ibm_plus_label, bar_index + label_x_offset_bars)
                if not na(ibm_minus_label)
                    label.set_x(ibm_minus_label, bar_index + label_x_offset_bars)
//------ ib indicator -----//
//-----att indicator-----//
// HTF Box Settings
att_group_candle = 'HTF Box Settings'

// HTF Box 1
att_htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP1', group=att_group_candle)
att_htfUser1  = input.string('1 Hour', 'TF1', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP1', group=att_group_candle)
att_bullC1    = input.color(#00000000, 'Bull1', inline='COL1', group=att_group_candle)
att_bearC1    = input.color(#00000000, 'Bear1', inline='COL1', group=att_group_candle)
att_showBoxes = input.bool(true, 'Show HTF Boxes', group=att_group_candle) // New input for toggling boxes

// Common Settings
att_group_common = 'Common Settings'
att_trans        = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=att_group_common)
att_lw           = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=att_group_common)
att_showNumbers  = input.bool(true, 'Show Candle Numbers', group=att_group_common)
att_numbersColor = input.color(color.white, 'Numbers Color', inline='NUM', group=att_group_common)
att_numbersSize  = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM', group=att_group_common)

// ATT Circle Settings
att_group_att = 'ATT Circle Settings'
att_showATT      = input.bool(true, 'Show ATT Circles', group=att_group_att)
att_showATTNumbers = input.bool(true, 'Show ATT Numbers on Circles', group=att_group_att)
att_showHistoricalATT = input.bool(true, 'Show Historical ATT Circles', group=att_group_att)
att_attColor1    = input.color(#8ee60ac4, 'HTF1 Color', inline='ATT_COL1', group=att_group_att)

// ATT Prediction Settings
att_group_prediction = 'ATT Prediction Settings'
att_showPrediction = input.bool(true, 'Show ATT Predictions', group=att_group_prediction)
att_predictionDistance = input.int(5, 'Show predictions X candles ahead', minval=1, maxval=20, group=att_group_prediction)
att_predictionColor = input.color(color.yellow, 'Prediction Label Color', group=att_group_prediction)
att_predictionSize = input.string(size.tiny, 'Prediction Label Size', options=[size.tiny, size.small, size.normal], group=att_group_prediction)

// The ATT candle numbers where arrow marks will be drawn
var att_att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

att_checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 : _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)
    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

att_f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0., htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.
    var int candleCount = 0

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 0
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        candleCount += 1
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c, candleCount]

att_f_getTF(_htf) =>
    _htf == '3 Mins' ? '3' :_htf == '5 Mins' ? '5' :_htf == '10 Mins' ? '10' :_htf == '15 Mins' ? '15' :_htf == '30 Mins' ? '30' :_htf == '45 Mins' ? '45' :_htf == '1 Hour' ? '60' :_htf == '2 Hours' ? '120' :_htf == '3 Hours' ? '180' :_htf == '4 Hours' ? '240' :_htf == '1 Day' ? 'D' :_htf == '1 Week' ? 'W' :_htf == '1 Month' ? 'M' :_htf == '3 Months' ? '3M' :_htf == '6 Months' ? '6M' :_htf == '1 Year' ? '12M' : na

// Global label arrays for each HTF box
var label[] att_candleLabelsHTF1 = array.new_label()
var label att_currentPredictionLabel = na

// Function to check if current bar should show ATT circle
att_f_checkATTCondition(_show, _htf) =>
    result = false
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        result := array.includes(att_att_numbers, currentCandle)
    result

// Function to get ATT number for current bar
att_f_getATTNumber(_show, _htf) =>
    attNumber = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        if array.includes(att_att_numbers, currentCandle)
            attNumber := currentCandle
    attNumber

// Function to get next ATT number and candles remaining
att_f_getNextATT(_show, _htf) =>
    nextATT = 0
    candlesRemaining = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1

        // Find the next ATT number
        for i = 0 to array.size(att_att_numbers) - 1
            attNum = array.get(att_att_numbers, i)
            if attNum > currentCandle
                nextATT := attNum
                candlesRemaining := attNum - currentCandle
                break

        // If no ATT number found in current cycle, get the first one from next cycle
        if nextATT == 0
            nextATT := array.get(att_att_numbers, 0)
            candlesRemaining := (60 - currentCandle) + nextATT

    [nextATT, candlesRemaining]

// Function to check if we should show prediction
att_f_shouldShowPrediction(_show, _htf) =>
    shouldShow = false
    if _show and att_showPrediction
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)
        shouldShow := candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
    shouldShow

// Function to draw candle numbers independently
att_f_drawCandleNumbers(_show, _htf, _labelArr) =>
    if _show and att_showNumbers
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        labelPos = low - (high - low) * 0.5
        candleLabel = label.new(bar_index, labelPos, str.tostring(candleCount + 1), style=label.style_label_center, textcolor=att_numbersColor, size=att_numbersSize, yloc=yloc.price, color=na, textalign=text.align_center)
        array.push(_labelArr, candleLabel)

        // Cleanup labels to prevent overload
        if array.size(_labelArr) > 480
            label.delete(array.shift(_labelArr))

// Function to get prediction data (returns values instead of modifying globals)
att_f_getPredictionData(_show, _htf) =>
    showPrediction = false
    predictionText = ""
    labelColor = att_predictionColor

    if _show and att_showPrediction
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)

        if candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
            showPrediction := true
            predictionText := candlesRemaining == 1 ? str.tostring(nextATT) + " NEXT!" : str.tostring(nextATT) + " in " + str.tostring(candlesRemaining)
            labelColor := candlesRemaining == 1 ? color.orange : att_predictionColor

    [showPrediction, predictionText, labelColor]

att_f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width, _labelArr, _htfNumber) =>
    if _show and att_showBoxes // Only draw boxes if att_showBoxes is true
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)

            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)

            // Cleanup labels to prevent overload
            if array.size(_labelArr) > 480
                label.delete(array.shift(_labelArr))

// ------------------------- Main Logic ------------------------- //

// Process HTF Box 1
att_htf1 = att_f_getTF(att_htfUser1)
att_supported1 = att_checkIf(timeframe.period, att_htf1)

if chart.is_standard
    // Draw HTF Box 1
    if att_supported1
        att_f_processCandles(att_htfCndl1, att_htf1, att_bullC1, att_bearC1, att_trans, att_lw, att_candleLabelsHTF1, 1)

// Calculate ATT conditions for HTF timeframe
att_attCondition1 = att_supported1 ? att_f_checkATTCondition(att_htfCndl1, att_htf1) : false
att_attNumber1 = att_supported1 ? att_f_getATTNumber(att_htfCndl1, att_htf1) : 0

// Determine candle color for positioning
att_isRedCandle = close < open
att_isGreenCandle = close >= open

// Calculate if we should show recent ATTs
// Use a simple approach: show if historical is enabled OR if we're within recent bars
att_recentBarsLimit = 120
att_barsFromEnd = last_bar_index - bar_index
att_showRecentATT = att_showHistoricalATT or (att_barsFromEnd <= att_recentBarsLimit)

// Plot ATT circles using plotshape (must be in global scope for historical display)
// HTF1 - With numbers - Red candles (above bar)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-3 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-11 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-17 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-29 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-41 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-47 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-53 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-59 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - With numbers - Green candles (below bar)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-3 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-11 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-17 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-29 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-41 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-47 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-53 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-59 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - Without numbers
plotshape(att_showATT and not att_showATTNumbers and att_attCondition1 and att_isRedCandle and att_showRecentATT, title="ATT HTF1 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small)
plotshape(att_showATT and not att_showATTNumbers and att_attCondition1 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small)

// Draw candle numbers for the HTF timeframe
if att_supported1 and att_htfCndl1
    att_f_drawCandleNumbers(true, att_htf1, att_candleLabelsHTF1)

// Handle prediction label in global scope
if not na(att_currentPredictionLabel)
    label.delete(att_currentPredictionLabel)
    att_currentPredictionLabel := na

if att_supported1 and att_htfCndl1
    [showPrediction, predictionText, labelColor] = att_f_getPredictionData(true, att_htf1)
    if showPrediction
        labelPos = (high + low) / 2  // Position at middle of candle
        att_currentPredictionLabel := label.new(bar_index + 1, labelPos, predictionText, style=label.style_label_left, textcolor=color.black, size=att_predictionSize, yloc=yloc.price, color=labelColor, textalign=text.align_left)

//-----att indicator-----





// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © tradeforopp

//@version=5
// indicator("Volume Profile [TFO]", "Volume Profile [TFO]", true, max_bars_back=5000, max_lines_count = 500, max_polylines_count = 100, max_labels_count = 500, max_boxes_count = 500)


var vpt_g_VP = "Volume Profile"
vpt_rows = input.int(200, "Rows", tooltip = "Number of price levels/rows", group = vpt_g_VP)
vpt_tf = input.timeframe("D", "Profile Timeframe", tooltip = "Timeframe the profile represents", group = vpt_g_VP)
vpt_ltf = input.timeframe("1", "Resolution Timeframe", tooltip = "Lower timeframe price data", group = vpt_g_VP)
vpt_extend = input.int(100, "Profile Extend %", 0, 100, tooltip = "Extension of profile to next session", group = vpt_g_VP)
vpt_vp_color = input.color(color.new(color.blue, 70), "Profile Color", tooltip = "Volume profile color", group = vpt_g_VP)
vpt_show_polyline = input.bool(false, "Show Polylines", tooltip = "Show volume profile as polylines", group = vpt_g_VP)

var vpt_g_VPOC = "Volume Point of Control"
vpt_show_vpoc = input.bool(true, "Show VPOC", inline = "VPOC", tooltip = "Show Volume Point of Control", group = vpt_g_VPOC)
vpt_vpoc_color = input.color(color.yellow, "", inline = "VPOC", group = vpt_g_VPOC)
vpt_ext_vpoc = input.bool(true, "Extend Last N VPOCs", inline = "EXT", tooltip = "Extend last N VPOC lines", group = vpt_g_VPOC)
vpt_ext_n_vpoc = input.int(5, "", 0, 100, inline = "EXT", group = vpt_g_VPOC)
vpt_vpoc_label_above = input.timeframe("D", "Show Labels Above", tooltip = "Show labels if TF >= this", group = vpt_g_VPOC)
vpt_vpoc_label_size = input.string('Normal', "Label Size", options = ['Auto', 'Tiny', 'Small', 'Normal', 'Large', 'Huge'], group = vpt_g_VPOC)
vpt_vpoc_width = input.int(2, "Line Width", group = vpt_g_VPOC)

var vpt_g_HVN = "High Volume Nodes"
vpt_show_hvn = input.bool(true, "Show Previous HVNs", inline = "HVN", group = vpt_g_HVN)
vpt_hvn_color_bull = input.color(color.new(color.teal, 70), "", inline = "HVN", group = vpt_g_HVN)
vpt_hvn_color_bear = input.color(color.new(color.red, 70), "", inline = "HVN", group = vpt_g_HVN)
vpt_hvn_strength = input.int(10, "HVN Strength", group = vpt_g_HVN)
vpt_hvn_type = input.string("Areas", "HVN Type", options = ['Levels','Areas'], group = vpt_g_HVN)
vpt_hvn_width = input.int(1, "Line Width", group = vpt_g_HVN)

vpt_get_label_size(x) =>
    switch x
        'Auto'   => size.auto
        'Tiny'   => size.tiny
        'Small'  => size.small
        'Normal' => size.normal
        'Large'  => size.large
        'Huge'   => size.huge

var vpt_vpoc = array.new_line()
var vpt_dates = array.new_label()

var vpt_values = array.new_float()
var vpt_x_vol = array.new_int()
var vpt_y_vol = array.new_float()

var vpt_hvn_lines = array.new_line()
var vpt_hvn_boxes = array.new_box()

var vpt_hvn_Ly = array.new_float()
var vpt_hvn_By = array.new_float()

var vpt_PLA = array.new<chart.point>()
var polyline vpt_PL = na
var line vpt_temp_vpoc = na

var int vpt_lb_idx = na
var int vpt_lb_time = na

vpt_ltf := timeframe.in_seconds(vpt_ltf) <= timeframe.in_seconds() ? vpt_ltf : ""
[vpt_ltf_H, vpt_ltf_L, vpt_ltf_V] = request.security_lower_tf(syminfo.tickerid, vpt_ltf, [high, low, volume])

if not na(vpt_lb_idx)
    vpt_lb = bar_index - vpt_lb_idx > 0 ? (bar_index - vpt_lb_idx) : 1
    vpt_y_max = ta.highest(high[1], vpt_lb)
    vpt_y_min = ta.lowest(low[1], vpt_lb)

    if timeframe.change(vpt_tf) or barstate.islast
        vpt_x_vol.clear()
        vpt_y_vol.clear()
        vpt_values.clear()

        for i = 0 to vpt_rows
            y = vpt_y_min + i * (vpt_y_max - vpt_y_min) / vpt_rows
            vpt_x_vol.push(vpt_lb_time)
            vpt_y_vol.push(y)
            vpt_values.push(0)

        for i = bar_index - vpt_lb_idx to 1
            vol = vpt_ltf_V[i]
            if vol.size() > 0
                for j = 0 to vpt_values.size() - 1
                    temp = vpt_y_vol.get(j)
                    for k = 0 to vol.size() - 1
                        H = vpt_ltf_H[i]
                        L = vpt_ltf_L[i]
                        V = vpt_ltf_V[i]
                        if H.get(k) >= temp and L.get(k) <= temp
                            add = math.floor(V.get(k) / ((H.get(k) - L.get(k)) / (vpt_y_max - vpt_y_min) / vpt_rows))
                            vpt_values.set(j, vpt_values.get(j) + add)

        max_y = vpt_y_vol.get(vpt_values.indexof(vpt_values.max()))
        sf = vpt_values.max() / (time[1] - vpt_lb_time) / (vpt_extend / 100)

        for j = 0 to vpt_values.size() - 1
            set = (vpt_lb_time + math.floor(vpt_values.get(j) / sf))
            vpt_x_vol.set(j, set)

        vpt_PLA.clear()
        vpt_PLA.push(chart.point.from_time(vpt_lb_time, vpt_y_min))
        for i = 0 to vpt_x_vol.size() - 1
            vpt_PLA.push(chart.point.from_time(vpt_x_vol.get(i), vpt_y_vol.get(i)))
        vpt_PLA.push(chart.point.from_time(vpt_lb_time, vpt_y_max))

        vpt_PL.delete()
        if timeframe.change(vpt_tf)
            if vpt_show_polyline
                polyline.new(vpt_PLA, curved = false, closed = true, line_color = vpt_vp_color, fill_color = vpt_vp_color, xloc = xloc.bar_time)
            vpt_temp_vpoc.delete()
            vpt_vpoc.unshift(line.new(vpt_lb_time, max_y, time, max_y, xloc = xloc.bar_time, color = vpt_show_vpoc ? vpt_vpoc_color : na, extend = vpt_ext_vpoc ? extend.right : extend.none, width = vpt_vpoc_width))
            if vpt_ext_vpoc and timeframe.in_seconds(vpt_tf) >= timeframe.in_seconds(vpt_vpoc_label_above)
                vpt_dates.unshift(label.new(bar_index, max_y, str.format("{0,date,short}", time("", session = "0000-0000", timezone = "America/New_York")), textcolor = vpt_show_vpoc ? vpt_vpoc_color : na, color = na, size = vpt_get_label_size(vpt_vpoc_label_size)))
        else
            if vpt_show_polyline
                vpt_PL := polyline.new(vpt_PLA, curved = false, closed = true, line_color = vpt_vp_color, fill_color = vpt_vp_color, xloc = xloc.bar_time)
            if na(vpt_temp_vpoc)
                vpt_temp_vpoc := line.new(vpt_lb_time, max_y, time, max_y, xloc = xloc.bar_time, color = vpt_show_vpoc ? vpt_vpoc_color : na, extend = vpt_ext_vpoc ? extend.right : extend.none, width = vpt_vpoc_width)
            vpt_temp_vpoc.set_y1(max_y)
            vpt_temp_vpoc.set_y2(max_y)
            vpt_temp_vpoc.set_x2(time)

vpt_get_hvn() =>
    if vpt_values.size() > vpt_hvn_strength
        for i = 0 to vpt_values.size() - 1
            start = vpt_values.get(i)
            valid = true
            for j = -vpt_hvn_strength to vpt_hvn_strength
                k = i + j
                if k < 0 or k > vpt_values.size() - 1
                    continue
                else
                    if j != 0 and vpt_values.get(k) > start
                        valid := false
                        break
            if valid
                idx = vpt_values.indexof(start)
                if idx != -1
                    y1 = vpt_y_vol.get(idx)
                    y2 = vpt_y_vol.get(idx)
                    val = vpt_y_vol.get(idx)
                    if i < vpt_values.size() - 1
                        for m = i to vpt_values.size() - 2
                            if vpt_values.get(m + 1) > vpt_values.get(m)
                                y1 := vpt_y_vol.get(m)
                                break
                    if i > 0
                        for m = i to 1
                            if vpt_values.get(m - 1) > vpt_values.get(m)
                                y2 := vpt_y_vol.get(m)
                                break
                    new_color = close[1] > math.avg(y1, y2) ? vpt_hvn_color_bull : vpt_hvn_color_bear
                    if vpt_hvn_type == "Levels"
                        if vpt_hvn_Ly.indexof(val) == -1
                            vpt_hvn_Ly.unshift(val)
                            vpt_hvn_lines.unshift(line.new(time, val, time + timeframe.in_seconds(vpt_tf)*1000, val, xloc = xloc.bar_time, color = color.new(new_color, 0), style = start == vpt_values.max() ? line.style_solid : line.style_dotted, width = vpt_hvn_width))
                    else
                        if vpt_hvn_By.indexof(y1) == -1
                            vpt_hvn_By.unshift(y1)
                            vpt_hvn_boxes.unshift(box.new(time, y1, time + timeframe.in_seconds(vpt_tf)*1000, y2, xloc = xloc.bar_time, bgcolor = new_color, border_color = na))

if timeframe.change(vpt_tf)
    vpt_lb_idx := bar_index
    vpt_lb_time := time

    if vpt_show_hvn
        vpt_hvn_lines.clear()
        vpt_hvn_boxes.clear()
        vpt_hvn_Ly.clear()
        vpt_hvn_By.clear()
        vpt_get_hvn()

    if vpt_ext_vpoc and vpt_vpoc.size() > vpt_ext_n_vpoc
        line.set_extend(vpt_vpoc.pop(), extend.none)
        if timeframe.in_seconds(vpt_tf) >= timeframe.in_seconds(vpt_vpoc_label_above)
            label.delete(vpt_dates.pop())

if vpt_dates.size() > 0
    for i = 0 to vpt_dates.size() - 1
        vpt_dates.get(i).set_x(bar_index + 20)




//@version=5
// indicator("Silver Bullet - Combined FVG & Signals", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// === Inputs ===
selectedTF         = input.string("1", "Selected FVG Timeframe", options=["1", "3", "5", "15"], group="Session")
showSB             = input.bool(true,     "Show 10–11 AM Session",        group="Session")
showHistoricalFVGs = input.bool(true,     "Show Historical FVGs",         group="Session")
extendLine         = input.bool(true,     "Extend Vertical Separators",   group="Session")
extendHLine        = input.bool(true,     "Extend Historical Lines",      group="Session & FVG")
boxShift           = input.int(1,         "Shift FVG Box Left", minval=0, group="Session")
historyDays        = input.int(50,         "Look-back (Days)", minval=1, maxval=500, group="Session & FVG")
labelSizeOpt       = input.string("Tiny", "Session Label Size", options=["Auto","Tiny","Small","Normal","Large","Huge"], group="Session")
labelColor         = input.color(color.white, "Session Label Color",       group="Session")
labelXOffset       = input.int(20,        "Session Label X Offset", minval=0,   group="Session")
labelYOffset       = input.float(0.0,     "Session Label Y Offset",           group="Session")
flipSource         = input.string("Wick", "Flip Source", options=["Wick","Close"], group="FVG Style")

// === Trading Signal Inputs ===
tradeLabelSize     = input.string("Normal", "Trade Label Size", options=["Tiny","Small","Normal","Large","Huge"], group="Trading Signals")
enableSBSignals    = input.bool(true,     "Enable SB Signals (Original)",     group="Trading Signals")
enableSMISignals   = input.bool(true,     "Enable SMI Signals",               group="Trading Signals")
smiLookback        = input.int(100,       "SMI Lookback Candles", minval=1, maxval=200, group="Trading Signals")
debugLabels        = input.bool(false,    "Show Debug Labels",                 group="Debug")
debugNumbering     = input.bool(false,    "Show Numbering Debug",              group="Debug")
showEngulfingDebug = input.bool(false,    "Show Engulfing Debug",              group="Debug")
showSMIDebug       = input.bool(false,    "Show SMI Debug",                    group="Debug")
showDetailedDebug  = input.bool(false,    "Show Detailed Debug",               group="Debug")

// === FVG Styles ===
currentFVGCol = input.color(#e8ed5e89,    "Current FVG Color",   group="Colors")
histFVGCol    = input.color(color.rgb(94, 134, 237, 80), "Historical FVG Color", group="Colors")
midlineColor  = input.color(#787b86,      "Midline Color", group="Colors")
invisBorder   = color.new(color.black, 100)

// === Label size mapping ===
labelSizeMap(x) =>
    switch x
        'Auto'   => size.auto
        'Tiny'   => size.tiny
        'Small'  => size.small
        'Normal' => size.normal
        'Large'  => size.large
        'Huge'   => size.huge

// === Session logic ===
timeSess(tf, s) => time(tf, s, "America/New_York")
SB_AM   = timeSess(timeframe.period, "1000-1100")
strAM   = SB_AM and not SB_AM[1]
n       = bar_index
minT    = syminfo.mintick
inAM    = timeSess(selectedTF, "1000-1100")
after10 = inAM and not strAM

// === Pull wick/close from selectedTF ===
[lf_low, lf_high, lf_close] = request.security(syminfo.tickerid, selectedTF, [low, high, close])

// === Session separators & storage ===
var line    vLine        = na
var line[]  sessHLines   = array.new_line()
var int[]   sessHTimes   = array.new_int()
var float[] sessHPrices  = array.new_float()
var label[] sessHLabels  = array.new_label()

// === Current session-line vars ===
var line   hLine   = na
var label  hLabel  = na
var float  hPrice  = na
var int    hStart  = na

// ── On new 10:00 AM session ──
if strAM and showSB
    vLine := line.new(n, close, n, close + minT, color=color.white, extend=extendLine ? extend.both : extend.none)
    if not na(hLine)
        line.set_extend(hLine, extend.none)
        line.set_x2(hLine, n)
        line.set_y2(hLine, hPrice)
        array.push(sessHLines, hLine)
        array.push(sessHTimes, time)
        array.push(sessHPrices, hPrice)
        array.push(sessHLabels, hLabel)
    hStart := n
    hPrice := open
    hLine  := line.new(hStart, hPrice, n, hPrice, color=color.white, style=line.style_dotted, extend=extend.none)
    d = dayofmonth < 10 ? "0"+str.tostring(dayofmonth) : str.tostring(dayofmonth)
    m = month      < 10 ? "0"+str.tostring(month)      : str.tostring(month)
    y = (year%100) < 10  ? "0"+str.tostring(year%100)   : str.tostring(year%100)
    dateStr = d + "/" + m + "/" + y
    hLabel := label.new(x=n+labelXOffset, y=hPrice+labelYOffset, text=dateStr, xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_right, size=labelSizeMap(labelSizeOpt), textcolor=labelColor, color=color.new(color.white,100))

// ── Update current session horizontal each bar ──
if not na(hLine)
    line.set_extend(hLine, extend.none)
    line.set_x2(hLine, n)
    line.set_y2(hLine, hPrice)
    label.set_x(hLabel, n+labelXOffset)
    label.set_y(hLabel, hPrice+labelYOffset)

// === FVG detection on 1-min ===
fvgDetect() =>
    bull = low > high[2] and close[1] > high[2]
    bear = high < low[2]  and close[1] < low[2]
    top  = bull ? low     : bear ? low[2]     : na
    bot  = bull ? high[2] : bear ? high        : na
    [bull, bear, top, bot]
[bull, bear, top, bot] = request.security(syminfo.tickerid, selectedTF, fvgDetect())

// === Historical FVG storage ===
var box[]   fvgBoxes = array.new_box()
var line[]  fvgMids  = array.new_line()
var bool[]  fvgBull  = array.new_bool()
var int[]   fvgTimes = array.new_int()

// === Single-session FVG state ===
var box    currBox  = na
var line   currMid  = na
var bool   drawn    = false
var bool   flipped  = false
var float  fvgTop   = na
var float  fvgBot   = na
var bool   isBull   = na

// === Trading Signal State Variables ===
var bool   swingConfirmed = false
var bool   tapped       = false
var int    tapBar       = na
var bool   waitEngulfing = false
var int    candlesSinceTap = 0
var float  lastUpLow     = na  // low of last up‑close candle
var float  lastDownHigh  = na  // high of last down‑close candle

// === SMI Signal State Variables (Historical FVG Only) ===
var int    smiBar        = na
var bool   waitSMIEngulfing = false
var float  smiLastUpLow     = na
var float  smiLastDownHigh  = na
var bool   smiHistoricalDetected = false

// ── At 10:00 AM stash last FVG & reset ──
if strAM
    if not na(currBox)
        array.push(fvgBoxes, currBox)
        array.push(fvgMids,   currMid)
        array.push(fvgBull,   isBull)
        array.push(fvgTimes,  time)
    drawn   := false
    flipped := false
    currBox := na
    currMid := na
    fvgTop  := na
    fvgBot  := na
    isBull  := na
    // Reset trading signal variables
    swingConfirmed := false
    tapped         := false
    waitEngulfing  := false
    candlesSinceTap := 0
    lastUpLow      := na
    lastDownHigh   := na
    // Reset SMI signal variables
    smiBar         := na
    waitSMIEngulfing := false
    smiLastUpLow   := na
    smiLastDownHigh := na
    smiHistoricalDetected := false

// ── Draw first FVG after 10:00 AM ──
if after10 and not drawn and (bull or bear)
    isBull  := bull
    fvgTop  := top
    fvgBot  := bot
    currBox := box.new(n-boxShift, fvgTop, n, fvgBot, border_color=invisBorder, bgcolor=currentFVGCol)
    currMid := line.new(n-boxShift, (fvgTop+fvgBot)/2, n, (fvgTop+fvgBot)/2, color=midlineColor, style=line.style_dashed)
    drawn   := true
    flipped := false
    if debugLabels
        label.new(bar_index,
                  (fvgTop+fvgBot)/2,
                  isBull ? "Bull FVG" : "Bear FVG",
                  xloc.bar_index,
                  yloc.price,
                  style     = label.style_label_center,
                  textcolor = color.white,
                  color     = isBull ? color.blue : color.orange,
                  size      = labelSizeMap("Small"))

// ── Extend & flip FVG mid-line ──
if drawn and not na(currBox)
    box.set_right(currBox, n)
    line.set_x2(currMid, n)
    if not flipped
        srcLow  = flipSource == "Wick" ? lf_low   : lf_close
        srcHigh = flipSource == "Wick" ? lf_high  : lf_close
        if isBull and srcLow < fvgBot
            box.set_bgcolor(currBox, currentFVGCol)
            box.set_border_color(currBox, invisBorder)
            flipped := true
        else if not isBull and srcHigh > fvgTop
            box.set_bgcolor(currBox, currentFVGCol)
            box.set_border_color(currBox, invisBorder)
            flipped := true

// === SMI (Smart Money Index) Calculation ===
x = input.int(25, "SMI Index Period", minval = 1, group="SMI Settings")
rr = input.int(14, "SMI Volume Flow Period", minval = 1, group="SMI Settings")
peakslen = input.int(500, "SMI Normalization Period", minval = 1, group="SMI Settings")
thr = input.float(0.9, "SMI High Interest Threshold", minval = 0.01, maxval = 0.99, group="SMI Settings")

dumb = ta.pvi-ta.ema(ta.pvi,255)
smart = ta.nvi-ta.ema(ta.nvi,255)

drsi = ta.rsi(dumb, rr)
srsi = ta.rsi(smart, rr)

r = srsi/drsi //ratio shows if smart money is buying from dumb money selling and vice versa

sums = math.sum(r, x)
peak = ta.highest(sums, peakslen)

index = sums/peak

smiCondition = index > thr

// SMI Arrow Display
plotshape(series= smiCondition ? 1 : na, title="High Smart Money Interest", color=color.rgb(233, 239, 233), style=shape.arrowup, size=size.normal, location=location.belowbar, force_overlay=true)

// === ZigZag Calculation (moved here to be accessible in SMI logic) ===
import DevLucem/ZigLib/1 as ZigZag
Depth = input.int(12, 'ZigZag Depth', minval=1, step=1, group="ZigZag Config")
Deviation = input.int(5, 'ZigZag Deviation', minval=1, step=1, group="ZigZag Config")
Backstep = input.int(2, 'ZigZag Backstep', minval=2, step=1, group="ZigZag Config")

// ZigZag Display Controls
showZigZagLines = input.bool(false, "Show ZigZag Lines", group="ZigZag Display")
showZigZagLabels = input.bool(false, "Show ZigZag Labels", group="ZigZag Display")
line_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group="ZigZag Display")
labels = input(0, "Labels Transparency", group="ZigZag Display")
upcolor = input(color.lime, 'Bull Color', group="ZigZag Display")
dncolor = input(color.red, 'Bear Color', group="ZigZag Display")
lines = input(0, "Lines Transparency", group="ZigZag Display")
background = input(80, "Background Transparency", group="ZigZag Display")
zlabel_size = switch input.int(3, "Label Size", minval=1, maxval=5, group="ZigZag Display")
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
repaint = input(true, 'Repaint Levels', group="ZigZag Display")
extend = input(false, "Extend ZigZag", group="ZigZag Display")

[direction, z1, z2] = ZigZag.zigzag(low, high, Depth, Deviation, Backstep)
string nowPoint = ""
var float lastPoint = z1.price[1]
if bool(ta.change(direction))
    lastPoint := z1.price[1]

// Calculate current ZigZag point
nowPoint := direction<0? (z2.price<lastPoint? "LL": "HL"): (z2.price>lastPoint? "HH": "LH")

// === ZigZag Display Logic ===
if showZigZagLines or showZigZagLabels
    line zz = na
    label point = na

    if repaint
        if showZigZagLines
            zz := line.new(z1, z2, xloc.bar_time, extend? extend.right: extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
        if showZigZagLabels
            point := label.new(z2, nowPoint, xloc.bar_time, yloc.price,
             color.new(direction<0? upcolor: dncolor, labels), direction>0? label.style_label_down: label.style_label_up, color.new(direction>0? upcolor: dncolor, labels), zlabel_size)
        if direction == direction[1]
            if showZigZagLines
                line.delete(zz[1])
            if showZigZagLabels
                label.delete(point[1])
        else
            if showZigZagLines
                line.set_extend(zz[1], extend.none)
    else
        if direction != direction[1]
            if showZigZagLines
                zz := line.new(z1[1], z2[1], xloc.bar_time, extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
            if showZigZagLabels
                point := label.new(z2[1], nowPoint, xloc.bar_time, yloc.price,
                 color.new(direction[1]<0? upcolor: dncolor, labels), direction[1]>0? label.style_label_down: label.style_label_up, color.new(direction[1]>0? upcolor: dncolor, labels), zlabel_size)

// === Function to check if price is touching any historical FVG ===
checkHistoricalFVGTouch() =>
    touchingHistFVG = false
    histFVGType = false  // false = bearish, true = bullish
    if array.size(fvgBoxes) > 0 and showHistoricalFVGs
        cutoffTime = time - historyDays * 86400000
        for i = 0 to array.size(fvgBoxes) - 1
            if array.get(fvgTimes, i) >= cutoffTime
                isBullHist = array.get(fvgBull, i)
                b = array.get(fvgBoxes, i)
                fvgTopHist = box.get_top(b)
                fvgBotHist = box.get_bottom(b)

                // Check if current price is touching this historical FVG
                if (isBullHist and low <= fvgTopHist and high >= fvgBotHist) or
                   (not isBullHist and high >= fvgBotHist and low <= fvgTopHist)
                    touchingHistFVG := true
                    histFVGType := isBullHist
                    break
    [touchingHistFVG, histFVGType]

[touchingHistFVG, histFVGType] = checkHistoricalFVGTouch()

// ── Detect the FVG "tap" for trading signals ──
if drawn and not tapped and after10
    // first confirm swing‑away
    if not swingConfirmed and ((isBull and close > fvgTop) or (not isBull and close < fvgBot))
        swingConfirmed := true
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Swing Confirmed",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.black,
                      color     = color.yellow,
                      size      = labelSizeMap("Small"))
    // then the actual tap
    else if swingConfirmed and ((isBull and low <= fvgTop) or (not isBull and high >= fvgBot))
        tapBar           := bar_index
        waitEngulfing    := true
        candlesSinceTap  := 0
        tapped           := true
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Tapped FVG",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.blue,
                      color     = color.new(color.white,90),
                      size      = labelSizeMap("Small"))
        if debugNumbering
            label.new(bar_index,
                      high + 20 * minT,
                      "TAP: Starting numbering from bar " + str.tostring(bar_index),
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.white,
                      color     = color.purple,
                      size      = labelSizeMap("Small"))

// ── SMI Signal Detection Logic (Historical FVG Focus) ──
if enableSMISignals and not waitSMIEngulfing  // No time filter - can happen anytime
    // Debug: Show all conditions
    if showDetailedDebug
        label.new(bar_index, high + 5*minT,
                  "SMI:" + str.tostring(enableSMISignals) + " Wait:" + str.tostring(waitSMIEngulfing) + " Touch:" + str.tostring(touchingHistFVG),
                  xloc.bar_index, yloc.price,
                  style = label.style_label_center,
                  textcolor = color.white,
                  color = color.blue,
                  size = labelSizeMap("Tiny"))

    // ONLY look for Historical FVG touches (more robust approach)
    if touchingHistFVG and not smiHistoricalDetected
        // Check if SMI was detected in the last 100 candles
        smiFoundInHistory = false
        smiCountInHistory = 0
        for i = 0 to smiLookback - 1
            if smiCondition[i]
                smiFoundInHistory := true
                smiCountInHistory := smiCountInHistory + 1

        if showDetailedDebug
            label.new(bar_index, low - 5*minT,
                      "HistFVG Touch! SMI found:" + str.tostring(smiFoundInHistory) + " Count:" + str.tostring(smiCountInHistory) + " FVGType:" + str.tostring(histFVGType),
                      xloc.bar_index, yloc.price,
                      style = label.style_label_center,
                      textcolor = color.white,
                      color = color.yellow,
                      size = labelSizeMap("Tiny"))

        if smiFoundInHistory
            smiHistoricalDetected := true
            smiBar := bar_index
            waitSMIEngulfing := true
            smiLastUpLow := na
            smiLastDownHigh := na
            if showSMIDebug
                label.new(bar_index,
                          close,
                          "SMI Found + Historical FVG Touch - Waiting for Engulfing",
                          xloc.bar_index,
                          yloc.price,
                          style     = label.style_label_center,
                          textcolor = color.white,
                          color     = color.orange,
                          size      = labelSizeMap("Small"))
        else if showDetailedDebug
            label.new(bar_index,
                      close,
                      "Historical FVG Touch - No SMI in last " + str.tostring(smiLookback) + " candles",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.white,
                      color     = color.gray,
                      size      = labelSizeMap("Tiny"))

// ── SMI + FVG ENGULFING SIGNAL LOGIC ──
if waitSMIEngulfing and enableSMISignals
    barsSinceSMI = bar_index - smiBar

    // Debug: Show waiting status
    if showDetailedDebug
        label.new(bar_index, close + 10*minT,
                  "Waiting SMI Engulf - Bars:" + str.tostring(barsSinceSMI) + " TouchHist:" + str.tostring(touchingHistFVG) + " HistDet:" + str.tostring(smiHistoricalDetected),
                  xloc.bar_index, yloc.price,
                  style = label.style_label_center,
                  textcolor = color.white,
                  color = color.purple,
                  size = labelSizeMap("Tiny"))

    // update last opposite‑close extremes for SMI signals
    if close > open
        smiLastUpLow := low
    else if close < open
        smiLastDownHigh := high

    if barsSinceSMI <= 10
        // SMI signals now ONLY work with historical FVGs
        if smiHistoricalDetected and touchingHistFVG
            // Smart direction detection for SMI reversal signals
            // Multiple methods to determine if price is approaching from above or below

            // Method 1: ZigZag direction (current trend)
            zzBearish = direction < 0  // ZigZag showing bearish trend (approaching from above)
            zzBullish = direction > 0  // ZigZag showing bullish trend (approaching from below)

            // Method 2: Recent price action (last 5 bars)
            recentHigh = ta.highest(high, 5)
            recentLow = ta.lowest(low, 5)
            fvgMidpoint = (fvgTop + fvgBot) / 2

            // If recent high is significantly above FVG and current price near FVG = approaching from above
            approachingFromAbove = (recentHigh > fvgTop + (fvgTop - fvgBot) * 0.5) and (close <= fvgTop)
            // If recent low is significantly below FVG and current price near FVG = approaching from below
            approachingFromBelow = (recentLow < fvgBot - (fvgTop - fvgBot) * 0.5) and (close >= fvgBot)

            // Method 3: Price momentum (EMA direction)
            ema20 = ta.ema(close, 20)
            ema50 = ta.ema(close, 50)
            momentumBearish = ema20 < ema20[1] and close < ema20  // Bearish momentum
            momentumBullish = ema20 > ema20[1] and close > ema20  // Bullish momentum

            // Combined direction assessment (majority vote)
            bearishSignals = (zzBearish ? 1 : 0) + (approachingFromAbove ? 1 : 0) + (momentumBearish ? 1 : 0)
            bullishSignals = (zzBullish ? 1 : 0) + (approachingFromBelow ? 1 : 0) + (momentumBullish ? 1 : 0)

            // Determine expected reversal direction
            expectBullishReversal = bearishSignals >= 2  // If bearish approach, expect bullish reversal
            expectBearishReversal = bullishSignals >= 2  // If bullish approach, expect bearish reversal

            if showDetailedDebug
                label.new(bar_index, close - 10*minT,
                          "SMI Direction - ZZ:" + str.tostring(direction) + " Above:" + str.tostring(approachingFromAbove) + " Below:" + str.tostring(approachingFromBelow) + " BearSig:" + str.tostring(bearishSignals) + " BullSig:" + str.tostring(bullishSignals) + " ExpBuy:" + str.tostring(expectBullishReversal) + " ExpSell:" + str.tostring(expectBearishReversal),
                          xloc.bar_index, yloc.price,
                          style = label.style_label_center,
                          textcolor = color.white,
                          color = color.aqua,
                          size = labelSizeMap("Tiny"))

            // SMI BUY: Expecting bullish reversal + bullish engulfing
            if expectBullishReversal and close > open and not na(smiLastDownHigh) and close >= smiLastDownHigh
                label.new(bar_index, low - 15*minT,
                          "SMI-Buy",
                          style=label.style_label_up,
                          textcolor=color.white,
                          color=color.lime,
                          size=labelSizeMap(tradeLabelSize))
                waitSMIEngulfing := false
                smiLastUpLow := na
                smiLastDownHigh := na
                smiHistoricalDetected := false
                if showSMIDebug
                    label.new(bar_index, close, "SMI Buy: Bearish Approach + Bullish Reversal (Signals:" + str.tostring(bearishSignals) + ")",
                             xloc.bar_index, yloc.price,
                             style     = label.style_label_center,
                             textcolor = color.white,
                             color     = color.lime,
                             size      = labelSizeMap("Small"))

            // SMI SELL: Expecting bearish reversal + bearish engulfing
            else if expectBearishReversal and close < open and not na(smiLastUpLow) and close <= smiLastUpLow
                label.new(bar_index, high + 15*minT,
                          "SMI-Sell",
                          style=label.style_label_down,
                          textcolor=color.white,
                          color=color.maroon,
                          size=labelSizeMap(tradeLabelSize))
                waitSMIEngulfing := false
                smiLastUpLow := na
                smiLastDownHigh := na
                smiHistoricalDetected := false
                if showSMIDebug
                    label.new(bar_index, close, "SMI Sell: Bullish Approach + Bearish Reversal (Signals:" + str.tostring(bullishSignals) + ")",
                             xloc.bar_index, yloc.price,
                             style     = label.style_label_center,
                             textcolor = color.white,
                             color     = color.maroon,
                             size      = labelSizeMap("Small"))

            // Debug: Show detailed signal check with smart direction detection
            else if showDetailedDebug
                buyCheck = expectBullishReversal and close > open and not na(smiLastDownHigh) and close >= smiLastDownHigh
                sellCheck = expectBearishReversal and close < open and not na(smiLastUpLow) and close <= smiLastUpLow

                debugText = "SMI Smart Check - Buy:" + str.tostring(buyCheck) + " Sell:" + str.tostring(sellCheck)
                debugText := debugText + " ExpBullRev:" + str.tostring(expectBullishReversal) + " ExpBearRev:" + str.tostring(expectBearishReversal)
                debugText := debugText + " BearSig:" + str.tostring(bearishSignals) + " BullSig:" + str.tostring(bullishSignals)
                debugText := debugText + " C>O:" + str.tostring(close > open) + " C<O:" + str.tostring(close < open)
                debugText := debugText + " DownHigh:" + str.tostring(smiLastDownHigh) + " UpLow:" + str.tostring(smiLastUpLow)

                label.new(bar_index, close,
                         debugText,
                         xloc.bar_index, yloc.price,
                         style = label.style_label_center,
                         textcolor = color.white,
                         color = color.red,
                         size = labelSizeMap("Tiny"))





    else
        // no SMI engulf within 10 bars → give up
        waitSMIEngulfing := false
        smiLastUpLow := na
        smiLastDownHigh := na
        smiHistoricalDetected := false
        if showSMIDebug
            label.new(bar_index, close, "SMI: No Engulfing Found - Stopped after 10 candles",
                     xloc.bar_index, yloc.price,
                     style     = label.style_label_center,
                     textcolor = color.white,
                     color     = color.gray,
                     size      = labelSizeMap("Small"))

// ── ORIGINAL SB SIGNAL LOGIC (Completely Independent from SMI) ──
if enableSBSignals and waitEngulfing  // Only depends on SB signals being enabled
    barsSince = bar_index - tapBar

    // update last opposite‑close extremes for SB signals
    if close > open
        lastUpLow := low
    else if close < open
        lastDownHigh := high

    if barsSince <= 10
        // SB BUY: bullish engulf = green close above last down-candle's high
        if isBull and close > open and not na(lastDownHigh) and close >= lastDownHigh
            label.new(bar_index, low - 10*minT,
                      "SB-Buy",
                      style=label.style_label_up,
                      textcolor=color.white,
                      color=color.green,
                      size=labelSizeMap(tradeLabelSize))
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "SB Bullish Engulfing: Green close above last red high",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.green,
                         size      = labelSizeMap("Small"))

        // SB SELL: bearish engulf = red close below last up-candle's low
        else if not isBull and close < open and not na(lastUpLow) and close <= lastUpLow
            label.new(bar_index, high + 10*minT,
                      "SB-Sell",
                      style=label.style_label_down,
                      textcolor=color.white,
                      color=color.red,
                      size=labelSizeMap(tradeLabelSize))
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "SB Bearish Engulfing: Red close below last green low",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.red,
                         size      = labelSizeMap("Small"))

    else
        // no SB engulf within 10 bars → give up
        waitEngulfing := false
        lastUpLow := na
        lastDownHigh := na
        if showEngulfingDebug
            label.new(bar_index, (fvgTop+fvgBot)/2, "SB: No Engulfing Found - Stopped after 10 candles",
                     xloc.bar_index, yloc.price,
                     style     = label.style_label_center,
                     textcolor = color.white,
                     color     = color.gray,
                     size      = labelSizeMap("Small"))

// ── Render last X days of historical FVGs ──
if array.size(fvgBoxes) > 0 and showHistoricalFVGs
    cutoffTime = time - historyDays * 86400000
    for i = 0 to array.size(fvgBoxes) - 1
        if array.get(fvgTimes, i) >= cutoffTime
            b = array.get(fvgBoxes, i)
            m = array.get(fvgMids,  i)
            box.set_right(b, n)
            box.set_bgcolor(b, histFVGCol)
            line.set_x2(m, n)

// ── Extend & label last X days of session horizontals ──
if array.size(sessHLines) > 0 and extendHLine
    cutoffSess = time - historyDays * 86400000
    for i = 0 to array.size(sessHLines) - 1
        if array.get(sessHTimes, i) >= cutoffSess
            ln    = array.get(sessHLines,  i)
            price = array.get(sessHPrices, i)
            lb    = array.get(sessHLabels, i)
            line.set_extend(ln, extend.none)
            line.set_x2(ln, n)
            line.set_y2(ln, price)
            label.set_x(lb, n + labelXOffset)
            label.set_y(lb, price + labelYOffset)

// ── Timeframe warning ──
var table tab = table.new(position=position.top_right, columns=1, rows=1)
if barstate.islast and timeframe.in_seconds(timeframe.period) > 15 * 60
    table.cell(tab, 0, 0, "Use timeframe ≤ 15 min", text_color=color.red)

// Alert condition for SMI
alertcondition(condition=smiCondition, title="High Smart Money Interest Alert", message="High Smart Money Interest detected!")








// zig-zag

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Dev Lucem

//@version=5
//@author=devlucem

//
//       THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
//       THE ZIGZAG SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
//       I HOPE U LOVE IT
//






// Optional: Show direction arrow
plotarrow(direction, "direction", display=display.status_line)


// ////////
// // Declare Meal Was Sweet By Force
alertcondition(nowPoint == "HH" and z2.price != z2.price[1], "New Higher High", 'Zigzag on {{ticker}} higher higher high detected at {{time}}')
alertcondition(nowPoint == "LH" and z2.price != z2.price[1], "New Lower High", 'Zigzag on {{ticker}} higher lower high detected at {{time}}')
alertcondition(nowPoint == "HL" and z2.price != z2.price[1], "New Higher Low", 'Zigzag on {{ticker}} higher lower low detected at {{time}}')
alertcondition(nowPoint == "LL" and z2.price != z2.price[1], "New Lower Low", 'Zigzag on {{ticker}} lower low detected at {{time}}')
alertcondition(direction != direction[1], 'Direction Changed', 'Zigzag on {{ticker}} direction changed at {{time}}')
alertcondition(direction != direction[1] and direction>0, 'Bullish Direction', 'Zigzag on {{ticker}} bullish direction at {{time}}')
alertcondition(direction != direction[1] and direction<0, 'Bearish Direction', 'Zigzag on {{ticker}} bearish direction at {{time}}')

if direction != direction[1]
    alert((direction<0? "Bearish": "Bullish") + " Direction Final ", alert.freq_once_per_bar_close)