
//@version=5
indicator("Initial Balance", shorttitle="IB", overlay=true, max_bars_back=5000)
// === INPUTS ===
// First IB Set
group_ib1 = "IB Set 1"
enable_ib1               = input.bool(true,  "Enable IB Set 1",                                     group=group_ib1)
ib_session               = input.session("0820-0920", title="Calculation period for the initial balance", group=group_ib1)
show_extra_levels        = input.bool(true,  "Show extra levels (IBH x2 & IBL x2)",                 group=group_ib1)
show_intermediate_levels = input.bool(true,  "Show intermediate levels (50%)",                      group=group_ib1)
show_ib_calculation_area = input.bool(true,  "Initial balance calculation period coloration",      group=group_ib1)
fill_ib_areas            = input.bool(true,  "Colour IB areas",                                    group=group_ib1)
only_current_levels      = input.bool(false, "Only display the current IB Levels",                 group=group_ib1)
only_current_zone        = input.bool(false, "Only display the current IB calculation area",      group=group_ib1)

// Second IB Set
group_ib2 = "IB Set 2"
enable_ib2               = input.bool(false, "Enable IB Set 2",                                     group=group_ib2)
ib_session2              = input.session("0000-0100", title="Calculation period for the initial balance 2", group=group_ib2)
show_extra_levels2       = input.bool(true,  "Show extra levels (IBH x2 & IBL x2)",                 group=group_ib2)
show_intermediate_levels2 = input.bool(true,  "Show intermediate levels (50%)",                      group=group_ib2)
show_ib_calculation_area2 = input.bool(true,  "Initial balance calculation period coloration",      group=group_ib2)
fill_ib_areas2           = input.bool(true,  "Colour IB areas",                                    group=group_ib2)
only_current_levels2     = input.bool(false, "Only display the current IB Levels",                 group=group_ib2)
only_current_zone2       = input.bool(false, "Only display the current IB calculation area",      group=group_ib2)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === DRAWING PARAMETERS ===
// IB Set 1 Drawing Parameters
group_draw1 = "IB Set 1 - Drawing"
lvl_width         = input.int(3,           "Daily price level width",                               group=group_draw1)
high_col          = input.color(#ffa726,   "Initial balance high levels color",                group=group_draw1)
low_col           = input.color(#ffa726,     "Initial balance low levels color",                 group=group_draw1)
middle_col        = input.color(#ffa726,       "50% initial balance color",                        group=group_draw1)
main_levels_style = input.string("Dotted",  "Main levels line style",     options=["Solid","Dashed","Dotted"], group=group_draw1)
ext_levels_style  = input.string("Dotted", "Extended levels line style", options=["Solid","Dashed","Dotted"], group=group_draw1)
int_levels_style  = input.string("Dotted", "Intermediate levels line style", options=["Solid","Dashed","Dotted"], group=group_draw1)
fill_ib_color     = input.color(#b8851faa, "IB area background color",                         group=group_draw1)

// IB Set 2 Drawing Parameters
group_draw2 = "IB Set 2 - Drawing"
lvl_width2         = input.int(2,           "Daily price level width",                               group=group_draw2)
high_col2          = input.color(color.white,   "Initial balance high levels color",                group=group_draw2)
low_col2           = input.color(color.white, "Initial balance low levels color",                 group=group_draw2)
middle_col2        = input.color(color.white, "50% initial balance color",                        group=group_draw2)
main_levels_style2 = input.string("Solid",  "Main levels line style",     options=["Solid","Dashed","Dotted"], group=group_draw2)
ext_levels_style2  = input.string("Solid", "Extended levels line style", options=["Solid","Dashed","Dotted"], group=group_draw2)
int_levels_style2  = input.string("Solid", "Intermediate levels line style", options=["Solid","Dashed","Dotted"], group=group_draw2)
fill_ib_color2     = input.color(#1e88e5aa, "IB area background color",                         group=group_draw2)

ext = extend.none  // manage extension manually

// === OVERLAP SIGNAL SETTINGS ===
group_signals = "Overlap Signals"
enable_overlap_signals = input.bool(true, "Enable Overlap Signals", group=group_signals)
proximity_threshold = input.float(0.5, "Proximity Threshold (Points)", minval=0.1, step=0.1, group=group_signals)
show_overlap_boxes = input.bool(true, "Show Overlap Boxes", group=group_signals)
overlap_box_color = input.color(color.blue, "Overlap Box Border Color", group=group_signals)
signal_label_size = input.string("Normal", "Signal Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_signals)
buy_signal_color = input.color(color.lime, "Buy Signal Color", group=group_signals)
sell_signal_color = input.color(color.red, "Sell Signal Color", group=group_signals)

// Enhanced Signal Conditions
group_enhanced = "Enhanced Signal Conditions"
require_price_travel = input.bool(true, "Require Price Travel Through Levels", group=group_enhanced)
lookback_bars = input.int(20, "Lookback Bars for Price Travel", minval=5, maxval=100, group=group_enhanced)
require_candle_patterns = input.bool(true, "Require Candle Patterns", group=group_enhanced)
enable_engulfing = input.bool(true, "Enable Engulfing Pattern", group=group_enhanced)
enable_doji = input.bool(true, "Enable Doji Pattern", group=group_enhanced)
enable_hammer = input.bool(true, "Enable Hammer Pattern", group=group_enhanced)

// Smart Box Settings
group_smart_box = "Smart Box Settings"
box_height = input.float(2.0, "Fixed Box Height (Points)", minval=0.5, step=0.1, group=group_smart_box)
box_transparency = input.int(90, "Box Transparency %", minval=0, maxval=100, group=group_smart_box)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === UTILITIES ===
inSession(sess) => na(time(timeframe.period, sess)) == false
get_levels(n) =>
    float h = high[1]
    float l = low[1]
    for i = 1 to n
        if low[i]  < l
            l := low[i]
        if high[i] > h
            h := high[i]
    [h, l, (h + l) / 2]

// Function to manage line arrays based on max_periods
max_periods = 20
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// === STATE VARIABLES ===
var line_style_value = get_line_style(main_levels_style)
var line_style_value2 = get_line_style(main_levels_style2)
var label_size_value = get_label_size(label_size)

// === SESSION STATE ===
// IB Set 1
var float ib_delta       = na
var int   offset         = 0
var float[] delta_history = array.new_float(20)
ins = inSession(ib_session)

// IB Set 1 Level Values
var float ib1_high = na
var float ib1_low = na
var float ib1_mid = na
var float ib1_high_plus = na
var float ib1_low_minus = na
var float ib1_high_plus2 = na
var float ib1_low_minus2 = na
var float ib1_mid_plus = na
var float ib1_mid_minus = na

// IB Set 2
var float ib_delta2      = na
var int   offset2        = 0
var float[] delta_history2 = array.new_float(20)
ins2 = inSession(ib_session2)

// IB Set 2 Level Values
var float ib2_high = na
var float ib2_low = na
var float ib2_mid = na
var float ib2_high_plus = na
var float ib2_low_minus = na
var float ib2_high_plus2 = na
var float ib2_low_minus2 = na
var float ib2_mid_plus = na
var float ib2_mid_minus = na

bgcolor(enable_ib1 and show_ib_calculation_area and ins ? #673ab730 : na, title="IB calculation zone")
bgcolor(enable_ib2 and show_ib_calculation_area2 and ins2 ? #1e88e530 : na, title="IB calculation zone 2")

// === LINE AND LABEL ARRAYS ===
// IB Set 1 Arrays
var array<line> ibh_lines = array.new<line>()
var array<line> ibl_lines = array.new<line>()
var array<line> ibm_lines = array.new<line>()
var array<line> ib_plus_lines = array.new<line>()
var array<line> ib_minus_lines = array.new<line>()
var array<line> ib_plus2_lines = array.new<line>()
var array<line> ib_minus2_lines = array.new<line>()
var array<line> ibm_plus_lines = array.new<line>()
var array<line> ibm_minus_lines = array.new<line>()

var array<label> ibh_labels = array.new<label>()
var array<label> ibl_labels = array.new<label>()
var array<label> ibm_labels = array.new<label>()
var array<label> ib_plus_labels = array.new<label>()
var array<label> ib_minus_labels = array.new<label>()
var array<label> ib_plus2_labels = array.new<label>()
var array<label> ib_minus2_labels = array.new<label>()
var array<label> ibm_plus_labels = array.new<label>()
var array<label> ibm_minus_labels = array.new<label>()

// IB Set 2 Arrays
var array<line> ibh2_lines = array.new<line>()
var array<line> ibl2_lines = array.new<line>()
var array<line> ibm2_lines = array.new<line>()
var array<line> ib2_plus_lines = array.new<line>()
var array<line> ib2_minus_lines = array.new<line>()
var array<line> ib2_plus2_lines = array.new<line>()
var array<line> ib2_minus2_lines = array.new<line>()
var array<line> ibm2_plus_lines = array.new<line>()
var array<line> ibm2_minus_lines = array.new<line>()

var array<label> ibh2_labels = array.new<label>()
var array<label> ibl2_labels = array.new<label>()
var array<label> ibm2_labels = array.new<label>()
var array<label> ib2_plus_labels = array.new<label>()
var array<label> ib2_minus_labels = array.new<label>()
var array<label> ib2_plus2_labels = array.new<label>()
var array<label> ib2_minus2_labels = array.new<label>()
var array<label> ibm2_plus_labels = array.new<label>()
var array<label> ibm2_minus_labels = array.new<label>()

// === CURRENT LINES AND LABELS ===
// IB Set 1 Current Lines and Labels
var line  ibh_line        = na
var line  ibl_line        = na
var line  ibm_line        = na
var line  ib_plus_line    = na
var line  ib_minus_line   = na
var line  ib_plus2_line   = na
var line  ib_minus2_line  = na
var line  ibm_plus_line   = na
var line  ibm_minus_line  = na
var box   ib_area         = na

var label ibh_label       = na
var label ibl_label       = na
var label ibm_label       = na
var label ib_plus_label   = na
var label ib_minus_label  = na
var label ib_plus2_label  = na
var label ib_minus2_label = na
var label ibm_plus_label  = na
var label ibm_minus_label = na

// IB Set 2 Current Lines and Labels
var line  ibh2_line        = na
var line  ibl2_line        = na
var line  ibm2_line        = na
var line  ib2_plus_line    = na
var line  ib2_minus_line   = na
var line  ib2_plus2_line   = na
var line  ib2_minus2_line  = na
var line  ibm2_plus_line   = na
var line  ibm2_minus_line  = na
var box   ib2_area         = na

var label ibh2_label       = na
var label ibl2_label       = na
var label ibm2_label       = na
var label ib2_plus_label   = na
var label ib2_minus_label  = na
var label ib2_plus2_label  = na
var label ib2_minus2_label = na
var label ibm2_plus_label  = na
var label ibm2_minus_label = na

// === BUILD SESSION ===
// IB Set 1
if enable_ib1 and ins
    offset += 1

// IB Set 2
if enable_ib2 and ins2
    offset2 += 1

// IB Set 1 Session End
if enable_ib1 and ins[1] and not ins
    // calculate levels
    [h, l, m] = get_levels(offset)
    ib_delta := h - l

    // Store IB1 level values
    ib1_high := h
    ib1_low := l
    ib1_mid := m
    ib1_high_plus := h + ib_delta
    ib1_low_minus := l - ib_delta
    ib1_high_plus2 := h + ib_delta * 2
    ib1_low_minus2 := l - ib_delta * 2
    ib1_mid_plus := h + ib_delta / 2
    ib1_mid_minus := l - ib_delta / 2

    // update history
    if array.size(delta_history) >= 20
        array.shift(delta_history)
    array.push(delta_history, ib_delta)

    // Delete old labels when a new session starts
    if show_labels
        delete_all_labels(ibh_labels)
        delete_all_labels(ibl_labels)
        delete_all_labels(ibm_labels)
        delete_all_labels(ib_plus_labels)
        delete_all_labels(ib_minus_labels)
        delete_all_labels(ib_plus2_labels)
        delete_all_labels(ib_minus2_labels)
        delete_all_labels(ibm_plus_labels)
        delete_all_labels(ibm_minus_labels)

    // Create main level lines
    ibh_line := line.new(bar_index, h, bar_index, h, color=high_col, width=lvl_width, style=line_style_value)
    array.unshift(ibh_lines, ibh_line)
    manage_line_history(ibh_lines)

    ibl_line := line.new(bar_index, l, bar_index, l, color=low_col, width=lvl_width, style=line_style_value)
    array.unshift(ibl_lines, ibl_line)
    manage_line_history(ibl_lines)

    // Main level labels
    if show_labels
        ibh_label := label.new(bar_index + label_x_offset_bars, h + label_y_offset, "IBH" + (show_price_in_label ? str.format(" ({0})", h) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
        array.push(ibh_labels, ibh_label)

        ibl_label := label.new(bar_index + label_x_offset_bars, l + label_y_offset, "IBL" + (show_price_in_label ? str.format(" ({0})", l) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
        array.push(ibl_labels, ibl_label)

    // intermediate 50% line
    if show_intermediate_levels
        ibm_line := line.new(bar_index, m, bar_index, m, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
        array.unshift(ibm_lines, ibm_line)
        manage_line_history(ibm_lines)

        if show_labels
            ibm_label := label.new(bar_index + label_x_offset_bars, m + label_y_offset, "IBM" + (show_price_in_label ? str.format(" ({0})", m) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
            array.push(ibm_labels, ibm_label)

    // extra levels
    if show_extra_levels
        ib_plus_line := line.new(bar_index, h + ib_delta, bar_index, h + ib_delta, color=high_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_plus_lines, ib_plus_line)
        manage_line_history(ib_plus_lines)

        ib_minus_line := line.new(bar_index, l - ib_delta, bar_index, l - ib_delta, color=low_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_minus_lines, ib_minus_line)
        manage_line_history(ib_minus_lines)

        ib_plus2_line := line.new(bar_index, h + ib_delta * 2, bar_index, h + ib_delta * 2, color=high_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_plus2_lines, ib_plus2_line)
        manage_line_history(ib_plus2_lines)

        ib_minus2_line := line.new(bar_index, l - ib_delta * 2, bar_index, l - ib_delta * 2, color=low_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_minus2_lines, ib_minus2_line)
        manage_line_history(ib_minus2_lines)

        if show_labels
            ib_plus_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta) + label_y_offset, "IBH+Δ" + (show_price_in_label ? str.format(" ({0})", h + ib_delta) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
            array.push(ib_plus_labels, ib_plus_label)

            ib_minus_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta) + label_y_offset, "IBL-Δ" + (show_price_in_label ? str.format(" ({0})", l - ib_delta) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
            array.push(ib_minus_labels, ib_minus_label)

            ib_plus2_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta * 2) + label_y_offset, "IBH+2Δ" + (show_price_in_label ? str.format(" ({0})", h + ib_delta * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
            array.push(ib_plus2_labels, ib_plus2_label)

            ib_minus2_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta * 2) + label_y_offset, "IBL-2Δ" + (show_price_in_label ? str.format(" ({0})", l - ib_delta * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
            array.push(ib_minus2_labels, ib_minus2_label)

        if show_intermediate_levels
            ibm_plus_line := line.new(bar_index, h + ib_delta / 2, bar_index, h + ib_delta / 2, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
            array.unshift(ibm_plus_lines, ibm_plus_line)
            manage_line_history(ibm_plus_lines)

            ibm_minus_line := line.new(bar_index, l - ib_delta / 2, bar_index, l - ib_delta / 2, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
            array.unshift(ibm_minus_lines, ibm_minus_line)
            manage_line_history(ibm_minus_lines)

            if show_labels
                ibm_plus_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta / 2) + label_y_offset, "IBM+Δ/2" + (show_price_in_label ? str.format(" ({0})", h + ib_delta / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
                array.push(ibm_plus_labels, ibm_plus_label)

                ibm_minus_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta / 2) + label_y_offset, "IBM-Δ/2" + (show_price_in_label ? str.format(" ({0})", l - ib_delta / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
                array.push(ibm_minus_labels, ibm_minus_label)

    // fill the IB area
    if fill_ib_areas
        ib_area := box.new(bar_index[offset], h, bar_index, l, bgcolor=fill_ib_color, border_color=color.new(color.black, 100))

    offset := 0

// IB Set 2 Session End
if enable_ib2 and ins2[1] and not ins2
    // calculate levels
    [h2, l2, m2] = get_levels(offset2)
    ib_delta2 := h2 - l2

    // Store IB2 level values
    ib2_high := h2
    ib2_low := l2
    ib2_mid := m2
    ib2_high_plus := h2 + ib_delta2
    ib2_low_minus := l2 - ib_delta2
    ib2_high_plus2 := h2 + ib_delta2 * 2
    ib2_low_minus2 := l2 - ib_delta2 * 2
    ib2_mid_plus := h2 + ib_delta2 / 2
    ib2_mid_minus := l2 - ib_delta2 / 2

    // update history
    if array.size(delta_history2) >= 20
        array.shift(delta_history2)
    array.push(delta_history2, ib_delta2)

    // Delete old labels when a new session starts
    if show_labels
        delete_all_labels(ibh2_labels)
        delete_all_labels(ibl2_labels)
        delete_all_labels(ibm2_labels)
        delete_all_labels(ib2_plus_labels)
        delete_all_labels(ib2_minus_labels)
        delete_all_labels(ib2_plus2_labels)
        delete_all_labels(ib2_minus2_labels)
        delete_all_labels(ibm2_plus_labels)
        delete_all_labels(ibm2_minus_labels)

    // Create main level lines
    ibh2_line := line.new(bar_index, h2, bar_index, h2, color=high_col2, width=lvl_width2, style=line_style_value2)
    array.unshift(ibh2_lines, ibh2_line)
    manage_line_history(ibh2_lines)

    ibl2_line := line.new(bar_index, l2, bar_index, l2, color=low_col2, width=lvl_width2, style=line_style_value2)
    array.unshift(ibl2_lines, ibl2_line)
    manage_line_history(ibl2_lines)

    // Main level labels
    if show_labels
        ibh2_label := label.new(bar_index + label_x_offset_bars, h2 + label_y_offset, "IBH2" + (show_price_in_label ? str.format(" ({0})", h2) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col2, color=color.new(color.black, 100))
        array.push(ibh2_labels, ibh2_label)

        ibl2_label := label.new(bar_index + label_x_offset_bars, l2 + label_y_offset, "IBL2" + (show_price_in_label ? str.format(" ({0})", l2) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col2, color=color.new(color.black, 100))
        array.push(ibl2_labels, ibl2_label)

    // intermediate 50% line
    if show_intermediate_levels2
        ibm2_line := line.new(bar_index, m2, bar_index, m2, color=middle_col2, width=lvl_width2, style=get_line_style(int_levels_style2))
        array.unshift(ibm2_lines, ibm2_line)
        manage_line_history(ibm2_lines)

        if show_labels
            ibm2_label := label.new(bar_index + label_x_offset_bars, m2 + label_y_offset, "IBM2" + (show_price_in_label ? str.format(" ({0})", m2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col2, color=color.new(color.black, 100))
            array.push(ibm2_labels, ibm2_label)

    // extra levels
    if show_extra_levels2
        ib2_plus_line := line.new(bar_index, h2 + ib_delta2, bar_index, h2 + ib_delta2, color=high_col2, width=lvl_width2, style=get_line_style(ext_levels_style2))
        array.unshift(ib2_plus_lines, ib2_plus_line)
        manage_line_history(ib2_plus_lines)

        ib2_minus_line := line.new(bar_index, l2 - ib_delta2, bar_index, l2 - ib_delta2, color=low_col2, width=lvl_width2, style=get_line_style(ext_levels_style2))
        array.unshift(ib2_minus_lines, ib2_minus_line)
        manage_line_history(ib2_minus_lines)

        ib2_plus2_line := line.new(bar_index, h2 + ib_delta2 * 2, bar_index, h2 + ib_delta2 * 2, color=high_col2, width=lvl_width2, style=get_line_style(ext_levels_style2))
        array.unshift(ib2_plus2_lines, ib2_plus2_line)
        manage_line_history(ib2_plus2_lines)

        ib2_minus2_line := line.new(bar_index, l2 - ib_delta2 * 2, bar_index, l2 - ib_delta2 * 2, color=low_col2, width=lvl_width2, style=get_line_style(ext_levels_style2))
        array.unshift(ib2_minus2_lines, ib2_minus2_line)
        manage_line_history(ib2_minus2_lines)

        if show_labels
            ib2_plus_label := label.new(bar_index + label_x_offset_bars, (h2 + ib_delta2) + label_y_offset, "IBH2+Δ" + (show_price_in_label ? str.format(" ({0})", h2 + ib_delta2) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col2, color=color.new(color.black, 100))
            array.push(ib2_plus_labels, ib2_plus_label)

            ib2_minus_label := label.new(bar_index + label_x_offset_bars, (l2 - ib_delta2) + label_y_offset, "IBL2-Δ" + (show_price_in_label ? str.format(" ({0})", l2 - ib_delta2) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col2, color=color.new(color.black, 100))
            array.push(ib2_minus_labels, ib2_minus_label)

            ib2_plus2_label := label.new(bar_index + label_x_offset_bars, (h2 + ib_delta2 * 2) + label_y_offset, "IBH2+2Δ" + (show_price_in_label ? str.format(" ({0})", h2 + ib_delta2 * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col2, color=color.new(color.black, 100))
            array.push(ib2_plus2_labels, ib2_plus2_label)

            ib2_minus2_label := label.new(bar_index + label_x_offset_bars, (l2 - ib_delta2 * 2) + label_y_offset, "IBL2-2Δ" + (show_price_in_label ? str.format(" ({0})", l2 - ib_delta2 * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col2, color=color.new(color.black, 100))
            array.push(ib2_minus2_labels, ib2_minus2_label)

        if show_intermediate_levels2
            ibm2_plus_line := line.new(bar_index, h2 + ib_delta2 / 2, bar_index, h2 + ib_delta2 / 2, color=middle_col2, width=lvl_width2, style=get_line_style(int_levels_style2))
            array.unshift(ibm2_plus_lines, ibm2_plus_line)
            manage_line_history(ibm2_plus_lines)

            ibm2_minus_line := line.new(bar_index, l2 - ib_delta2 / 2, bar_index, l2 - ib_delta2 / 2, color=middle_col2, width=lvl_width2, style=get_line_style(int_levels_style2))
            array.unshift(ibm2_minus_lines, ibm2_minus_line)
            manage_line_history(ibm2_minus_lines)

            if show_labels
                ibm2_plus_label := label.new(bar_index + label_x_offset_bars, (h2 + ib_delta2 / 2) + label_y_offset, "IBM2+Δ/2" + (show_price_in_label ? str.format(" ({0})", h2 + ib_delta2 / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col2, color=color.new(color.black, 100))
                array.push(ibm2_plus_labels, ibm2_plus_label)

                ibm2_minus_label := label.new(bar_index + label_x_offset_bars, (l2 - ib_delta2 / 2) + label_y_offset, "IBM2-Δ/2" + (show_price_in_label ? str.format(" ({0})", l2 - ib_delta2 / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col2, color=color.new(color.black, 100))
                array.push(ibm2_minus_labels, ibm2_minus_label)

    // fill the IB area
    if fill_ib_areas2
        ib2_area := box.new(bar_index[offset2], h2, bar_index, l2, bgcolor=fill_ib_color2, border_color=color.new(color.black, 100))

    offset2 := 0

// === UPDATE LINES UNTIL NEXT SESSION ENDS ===
// Continue extending lines until the next session ends (not when it starts)
if not na(ibh_line) and not (ins[1] and not ins)
    // Update main level lines
    line.set_x2(ibh_line, bar_index)
    line.set_x2(ibl_line, bar_index)

    if show_labels
        label.set_x(ibh_label, bar_index + label_x_offset_bars)
        label.set_x(ibl_label, bar_index + label_x_offset_bars)

    if show_intermediate_levels and not na(ibm_line)
        line.set_x2(ibm_line, bar_index)
        if show_labels
            label.set_x(ibm_label, bar_index + label_x_offset_bars)

    if show_extra_levels
        if not na(ib_plus_line)
            line.set_x2(ib_plus_line, bar_index)
        if not na(ib_minus_line)
            line.set_x2(ib_minus_line, bar_index)
        if not na(ib_plus2_line)
            line.set_x2(ib_plus2_line, bar_index)
        if not na(ib_minus2_line)
            line.set_x2(ib_minus2_line, bar_index)

        if show_labels
            if not na(ib_plus_label)
                label.set_x(ib_plus_label, bar_index + label_x_offset_bars)
            if not na(ib_minus_label)
                label.set_x(ib_minus_label, bar_index + label_x_offset_bars)
            if not na(ib_plus2_label)
                label.set_x(ib_plus2_label, bar_index + label_x_offset_bars)
            if not na(ib_minus2_label)
                label.set_x(ib_minus2_label, bar_index + label_x_offset_bars)

        if show_intermediate_levels
            if not na(ibm_plus_line)
                line.set_x2(ibm_plus_line, bar_index)
            if not na(ibm_minus_line)
                line.set_x2(ibm_minus_line, bar_index)

            if show_labels
                if not na(ibm_plus_label)
                    label.set_x(ibm_plus_label, bar_index + label_x_offset_bars)
                if not na(ibm_minus_label)
                    label.set_x(ibm_minus_label, bar_index + label_x_offset_bars)

// === UPDATE LINES UNTIL NEXT SESSION ENDS - IB SET 2 ===
// Continue extending lines until the next session ends (not when it starts)
if enable_ib2 and not na(ibh2_line) and not (ins2[1] and not ins2)
    // Update main level lines
    line.set_x2(ibh2_line, bar_index)
    line.set_x2(ibl2_line, bar_index)

    if show_labels
        label.set_x(ibh2_label, bar_index + label_x_offset_bars)
        label.set_x(ibl2_label, bar_index + label_x_offset_bars)

    if show_intermediate_levels2 and not na(ibm2_line)
        line.set_x2(ibm2_line, bar_index)
        if show_labels
            label.set_x(ibm2_label, bar_index + label_x_offset_bars)

    if show_extra_levels2
        if not na(ib2_plus_line)
            line.set_x2(ib2_plus_line, bar_index)
        if not na(ib2_minus_line)
            line.set_x2(ib2_minus_line, bar_index)
        if not na(ib2_plus2_line)
            line.set_x2(ib2_plus2_line, bar_index)
        if not na(ib2_minus2_line)
            line.set_x2(ib2_minus2_line, bar_index)

        if show_labels
            if not na(ib2_plus_label)
                label.set_x(ib2_plus_label, bar_index + label_x_offset_bars)
            if not na(ib2_minus_label)
                label.set_x(ib2_minus_label, bar_index + label_x_offset_bars)
            if not na(ib2_plus2_label)
                label.set_x(ib2_plus2_label, bar_index + label_x_offset_bars)
            if not na(ib2_minus2_label)
                label.set_x(ib2_minus2_label, bar_index + label_x_offset_bars)

        if show_intermediate_levels2
            if not na(ibm2_plus_line)
                line.set_x2(ibm2_plus_line, bar_index)
            if not na(ibm2_minus_line)
                line.set_x2(ibm2_minus_line, bar_index)

            if show_labels
                if not na(ibm2_plus_label)
                    label.set_x(ibm2_plus_label, bar_index + label_x_offset_bars)
                if not na(ibm2_minus_label)
                    label.set_x(ibm2_minus_label, bar_index + label_x_offset_bars)










//@version=5
// indicator("Divergence for Many Indicators v5", overlay = true, max_bars_back = 1000, max_lines_count = 400, max_labels_count = 400)
prd = input.int(defval = 5, title = "Pivot Period", minval = 1, maxval = 50)
source = input.string(defval = "Close", title = "Source for Pivot Points", options = ["Close", "High/Low"])
searchdiv = input.string(defval = "Regular", title = "Divergence Type", options = ["Regular", "Hidden", "Regular/Hidden"])
showindis = input.string(defval = "Don't Show", title = "Show Indicator Names", options = ["Full", "First Letter", "Don't Show"])
showlimit = input.int(1, title="Minimum Number of Divergence", minval = 1, maxval = 11)
maxpp = input.int(defval = 10, title = "Maximum Pivot Points to Check", minval = 1, maxval = 20)
maxbars = input.int(defval = 100, title = "Maximum Bars to Check", minval = 30, maxval = 200)
shownum = input.bool(defval = true, title = "Show Divergence Number")
showlast = input.bool(defval = false, title = "Show Only Last Divergence")
dontconfirm = input.bool(defval = false, title = "Don't Wait for Confirmation")
showlines = input.bool(defval = false, title = "Show Divergence Lines")
showpivot = input.bool(defval = false, title = "Show Pivot Points")
calcmacd = input.bool(defval = true, title = "MACD")
calcmacda = input.bool(defval = true, title = "MACD Histogram")
calcrsi = input.bool(defval = true, title = "RSI")
calcstoc = input.bool(defval = true, title = "Stochastic")
calccci = input.bool(defval = true, title = "CCI")
calcmom = input.bool(defval = true, title = "Momentum")
calcobv = input.bool(defval = true, title = "OBV")
calcvwmacd = input.bool(true, title = "VWmacd")
calccmf = input.bool(true, title = "Chaikin Money Flow")
calcmfi = input.bool(true, title = "Money Flow Index")
calcext = input.bool(false, title = "Check External Indicator")
externalindi = input.source(defval = close, title = "External Indicator")
pos_reg_div_col = input.color(defval = color.yellow, title = "Positive Regular Divergence")
neg_reg_div_col = input.color(defval = color.navy, title = "Negative Regular Divergence")
pos_hid_div_col = input.color(defval = color.lime, title = "Positive Hidden Divergence")
neg_hid_div_col = input.color(defval = color.red, title = "Negative Hidden Divergence")
pos_div_text_col = input.color(defval = color.black, title = "Positive Divergence Text Color")
neg_div_text_col = input.color(defval = color.white, title = "Negative Divergence Text Color")
reg_div_l_style_ = input.string(defval = "Solid", title = "Regular Divergence Line Style", options = ["Solid", "Dashed", "Dotted"])
hid_div_l_style_ = input.string(defval = "Dashed", title = "Hidden Divergence Line Style", options = ["Solid", "Dashed", "Dotted"])
reg_div_l_width = input.int(defval = 2, title = "Regular Divergence Line Width", minval = 1, maxval = 5)
hid_div_l_width = input.int(defval = 1, title = "Hidden Divergence Line Width", minval = 1, maxval = 5)
showmas = input.bool(defval = false, title = "Show MAs 50 & 200", inline = "ma12")
cma1col = input.color(defval = color.lime, title = "", inline = "ma12")
cma2col = input.color(defval = color.red, title = "", inline = "ma12")

plot(showmas ? ta.sma(close, 50) : na, color = showmas ? cma1col : na)
plot(showmas ? ta.sma(close, 200) : na, color = showmas ? cma2col: na)

// set line styles
var reg_div_l_style = reg_div_l_style_ == "Solid" ? line.style_solid :
                       reg_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted
var hid_div_l_style = hid_div_l_style_ == "Solid" ? line.style_solid :
                       hid_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted


// get indicators
rsi = ta.rsi(close, 14) // RSI
[macd, signal, deltamacd] = ta.macd(close, 12, 26, 9) // MACD
moment = ta.mom(close, 10) // Momentum
cci = ta.cci(close, 10) // CCI
Obv = ta.obv // OBV
stk = ta.sma(ta.stoch(close, high, low, 14), 3) // Stoch
maFast = ta.vwma(close, 12), maSlow = ta.vwma(close, 26), vwmacd = maFast - maSlow // volume weighted macd
Cmfm = ((close-low) - (high-close)) / (high - low), Cmfv = Cmfm * volume, cmf = ta.sma(Cmfv, 21) / ta.sma(volume,21) // Chaikin money flow
Mfi = ta.mfi(close, 14) // Money Flow Index

// keep indicators names and colors in arrays
var indicators_name = array.new_string(11)
var div_colors = array.new_color(4)
if barstate.isfirst
    // names
    array.set(indicators_name, 0, showindis == "Full" ? "MACD" : "M")
    array.set(indicators_name, 1, showindis == "Full" ? "Hist" : "H")
    array.set(indicators_name, 2, showindis == "Full" ? "RSI" : "E")
    array.set(indicators_name, 3, showindis == "Full" ? "Stoch" : "S")
    array.set(indicators_name, 4, showindis == "Full" ? "CCI" : "C")
    array.set(indicators_name, 5, showindis == "Full" ? "MOM" : "M")
    array.set(indicators_name, 6, showindis == "Full" ? "OBV" : "O")
    array.set(indicators_name, 7, showindis == "Full" ? "VWMACD" : "V")
    array.set(indicators_name, 8, showindis == "Full" ? "CMF" : "C")
    array.set(indicators_name, 9, showindis == "Full" ? "MFI" : "M")
    array.set(indicators_name,10, showindis == "Full" ? "Extrn" : "X")
    //colors
    array.set(div_colors, 0, pos_reg_div_col)
    array.set(div_colors, 1, neg_reg_div_col)
    array.set(div_colors, 2, pos_hid_div_col)
    array.set(div_colors, 3, neg_hid_div_col)

// Check if we get new Pivot High Or Pivot Low
float ph = ta.pivothigh((source == "Close" ? close : high), prd, prd)
float pl = ta.pivotlow((source == "Close" ? close : low), prd, prd)
plotshape(not na(ph) and showpivot, text = "H",  style = shape.labeldown, color = color.new(color.white, 100), textcolor = color.red, location = location.abovebar, offset = -prd)
plotshape(not na(pl) and showpivot, text = "L",  style = shape.labelup, color = color.new(color.white, 100), textcolor = color.lime, location = location.belowbar, offset = -prd)

// keep values and positions of Pivot Highs/Lows in the arrays
var int maxarraysize = 20
var ph_positions = array.new_int(maxarraysize, 0)
var pl_positions = array.new_int(maxarraysize, 0)
var ph_vals = array.new_float(maxarraysize, 0.)
var pl_vals = array.new_float(maxarraysize, 0.)

// add PHs to the array
if not na(ph)
    array.unshift(ph_positions, bar_index)
    array.unshift(ph_vals, ph)
    if array.size(ph_positions) > maxarraysize
        array.pop(ph_positions)
        array.pop(ph_vals)

// add PLs to the array
if not na(pl)
    array.unshift(pl_positions, bar_index)
    array.unshift(pl_vals, pl)
    if array.size(pl_positions) > maxarraysize
        array.pop(pl_positions)
        array.pop(pl_vals)

// functions to check Regular Divergences and Hidden Divergences

// function to check positive regular or negative hidden divergence
// cond == 1 => positive_regular, cond == 2=> negative_hidden
positive_regular_positive_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : low
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src > src[1] or close > close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(pl_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(pl_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(pl_vals, x))) or
               (cond == 2 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(pl_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] < virtual_line1 or close[y] < virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// function to check negative regular or positive hidden divergence
// cond == 1 => negative_regular, cond == 2=> positive_hidden
negative_regular_negative_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : high
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src < src[1] or close < close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(ph_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(ph_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(ph_vals, x))) or
               (cond == 2 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(ph_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] > virtual_line1 or close[y] > virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// calculate 4 types of divergence if enabled in the options and return divergences in an array
calculate_divs(cond, indicator)=>
    divs = array.new_int(4, 0)
    array.set(divs, 0, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? positive_regular_positive_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 1, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? negative_regular_negative_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 2, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? positive_regular_positive_hidden_divergence(indicator, 2) : 0)
    array.set(divs, 3, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? negative_regular_negative_hidden_divergence(indicator, 2) : 0)
    divs

// array to keep all divergences
var all_divergences = array.new_int(44) // 11 indicators * 4 divergence = 44 elements
// set related array elements
array_set_divs(div_pointer, index)=>
    for x = 0 to 3
        array.set(all_divergences, index * 4 + x, array.get(div_pointer, x))

// set divergences array
array_set_divs(calculate_divs(calcmacd, macd), 0)
array_set_divs(calculate_divs(calcmacda, deltamacd), 1)
array_set_divs(calculate_divs(calcrsi, rsi), 2)
array_set_divs(calculate_divs(calcstoc, stk), 3)
array_set_divs(calculate_divs(calccci, cci), 4)
array_set_divs(calculate_divs(calcmom, moment), 5)
array_set_divs(calculate_divs(calcobv, Obv), 6)
array_set_divs(calculate_divs(calcvwmacd, vwmacd), 7)
array_set_divs(calculate_divs(calccmf, cmf), 8)
array_set_divs(calculate_divs(calcmfi, Mfi), 9)
array_set_divs(calculate_divs(calcext, externalindi), 10)

// check minimum number of divergence, if less than showlimit then delete all divergence
total_div = 0
for x = 0 to array.size(all_divergences) - 1
    total_div := total_div + math.round(math.sign(array.get(all_divergences, x)))

if total_div < showlimit
    array.fill(all_divergences, 0)

// keep line in an array
var pos_div_lines = array.new_line(0)
var neg_div_lines = array.new_line(0)
var pos_div_labels = array.new_label(0)
var neg_div_labels = array.new_label(0)

// remove old lines and labels if showlast option is enabled
delete_old_pos_div_lines()=>
    if array.size(pos_div_lines) > 0
        for j = 0 to array.size(pos_div_lines) - 1
            line.delete(array.get(pos_div_lines, j))
        array.clear(pos_div_lines)

delete_old_neg_div_lines()=>
    if array.size(neg_div_lines) > 0
        for j = 0 to array.size(neg_div_lines) - 1
            line.delete(array.get(neg_div_lines, j))
        array.clear(neg_div_lines)

delete_old_pos_div_labels()=>
    if array.size(pos_div_labels) > 0
        for j = 0 to array.size(pos_div_labels) - 1
            label.delete(array.get(pos_div_labels, j))
        array.clear(pos_div_labels)

delete_old_neg_div_labels()=>
    if array.size(neg_div_labels) > 0
        for j = 0 to array.size(neg_div_labels) - 1
            label.delete(array.get(neg_div_labels, j))
        array.clear(neg_div_labels)

// delete last creted lines and labels until we met new PH/PV
delete_last_pos_div_lines_label(n)=>
    if n > 0 and array.size(pos_div_lines) >= n
        asz = array.size(pos_div_lines)
        for j = 1 to n
            line.delete(array.get(pos_div_lines, asz - j))
            array.pop(pos_div_lines)
        if array.size(pos_div_labels) > 0
            label.delete(array.get(pos_div_labels, array.size(pos_div_labels) - 1))
            array.pop(pos_div_labels)

delete_last_neg_div_lines_label(n)=>
    if n > 0 and array.size(neg_div_lines) >= n
        asz = array.size(neg_div_lines)
        for j = 1 to n
            line.delete(array.get(neg_div_lines, asz - j))
            array.pop(neg_div_lines)
        if array.size(neg_div_labels) > 0
            label.delete(array.get(neg_div_labels, array.size(neg_div_labels) - 1))
            array.pop(neg_div_labels)

// variables for Alerts
pos_reg_div_detected = false
neg_reg_div_detected = false
pos_hid_div_detected = false
neg_hid_div_detected = false

// to remove lines/labels until we met new // PH/PL
var last_pos_div_lines = 0
var last_neg_div_lines = 0
var remove_last_pos_divs = false
var remove_last_neg_divs = false
if not na(pl)
    remove_last_pos_divs := false
    last_pos_div_lines := 0
if not na(ph)
    remove_last_neg_divs := false
    last_neg_div_lines := 0

// draw divergences lines and labels
divergence_text_top = ""
divergence_text_bottom = ""
distances = array.new_int(0)
dnumdiv_top = 0
dnumdiv_bottom = 0
top_label_col = color.white
bottom_label_col = color.white
old_pos_divs_can_be_removed = true
old_neg_divs_can_be_removed = true
startpoint = dontconfirm ? 0 : 1 // used for don't confirm option

for x = 0 to 10
    div_type = -1
    for y = 0 to 3
        if array.get(all_divergences, x * 4 + y) > 0 // any divergence?
            div_type := y
            if (y % 2) == 1
                dnumdiv_top := dnumdiv_top + 1
                top_label_col := array.get(div_colors, y)
            if (y % 2) == 0
                dnumdiv_bottom := dnumdiv_bottom + 1
                bottom_label_col := array.get(div_colors, y)
            if not array.includes(distances, array.get(all_divergences, x * 4 + y))  // line not exist ?
                array.push(distances, array.get(all_divergences, x * 4 + y))
                new_line = showlines ? line.new(x1 = bar_index - array.get(all_divergences, x * 4 + y),
                          y1 = (source == "Close" ? close[array.get(all_divergences, x * 4 + y)] :
                                           (y % 2) == 0 ? low[array.get(all_divergences, x * 4 + y)] :
                                                          high[array.get(all_divergences, x * 4 + y)]),
                          x2 = bar_index - startpoint,
                          y2 = (source == "Close" ? close[startpoint] :
                                           (y % 2) == 0 ? low[startpoint] :
                                                          high[startpoint]),
                          color = array.get(div_colors, y),
                          style = y < 2 ? reg_div_l_style : hid_div_l_style,
                          width = y < 2 ? reg_div_l_width : hid_div_l_width
                          )
                          : na
                if (y % 2) == 0
                    if old_pos_divs_can_be_removed
                        old_pos_divs_can_be_removed := false
                        if not showlast and remove_last_pos_divs
                            delete_last_pos_div_lines_label(last_pos_div_lines)
                            last_pos_div_lines := 0
                        if showlast
                            delete_old_pos_div_lines()
                    array.push(pos_div_lines, new_line)
                    last_pos_div_lines := last_pos_div_lines + 1
                    remove_last_pos_divs := true

                if (y % 2) == 1
                    if old_neg_divs_can_be_removed
                        old_neg_divs_can_be_removed := false
                        if not showlast and remove_last_neg_divs
                            delete_last_neg_div_lines_label(last_neg_div_lines)
                            last_neg_div_lines := 0
                        if showlast
                            delete_old_neg_div_lines()
                    array.push(neg_div_lines, new_line)
                    last_neg_div_lines := last_neg_div_lines + 1
                    remove_last_neg_divs := true

            // set variables for alerts
            if y == 0
                pos_reg_div_detected := true
            if y == 1
                neg_reg_div_detected := true
            if y == 2
                pos_hid_div_detected := true
            if y == 3
                neg_hid_div_detected := true
    // get text for labels
    if div_type >= 0
        divergence_text_top    := divergence_text_top    + ((div_type % 2) == 1 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")
        divergence_text_bottom := divergence_text_bottom + ((div_type % 2) == 0 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")


// draw labels
if showindis != "Don't Show" or shownum
    if shownum and dnumdiv_top > 0
        divergence_text_top := divergence_text_top + str.tostring(dnumdiv_top)
    if shownum and dnumdiv_bottom > 0
        divergence_text_bottom := divergence_text_bottom + str.tostring(dnumdiv_bottom)
    if divergence_text_top != ""
        if showlast
            delete_old_neg_div_labels()
        array.push(neg_div_labels,
                      label.new( x = bar_index,
                                 y = math.max(high, high[1]),
                                 text = divergence_text_top,
                                 color = top_label_col,
                                 textcolor = neg_div_text_col,
                                 style = label.style_label_down
                                 ))

    if divergence_text_bottom != ""
        if showlast
            delete_old_pos_div_labels()
        array.push(pos_div_labels,
                      label.new( x = bar_index,
                                 y = math.min(low, low[1]),
                                 text = divergence_text_bottom,
                                 color = bottom_label_col,
                                 textcolor = pos_div_text_col,
                                 style = label.style_label_up
                                 ))

// === OVERLAP DETECTION AND SIGNAL GENERATION ===
var signal_label_size_value = get_label_size(signal_label_size)

// Arrays to store overlap signals (similar to divergence arrays)
var overlap_buy_signals = array.new_label(0)
var overlap_sell_signals = array.new_label(0)
var overlap_boxes = array.new_box(0)

// Smart overlap box tracking
var box current_overlap_box = na
var bool overlap_box_active = false
var float latest_overlap_level = na
var int latest_overlap_bar = na
var string latest_overlap_source = na  // "ib1" or "ib2"

// Enhanced signal tracking (following silver bullet pattern)
var bool waitForBuySignal = false
var bool waitForSellSignal = false
var int divergenceBar = na
var int barsSinceDivergence = 0

// Function to check if two levels overlap within proximity threshold
levels_overlap(level1, level2) =>
    not na(level1) and not na(level2) and math.abs(level1 - level2) <= proximity_threshold

// Function to check if price has traveled through a level in lookback period
price_traveled_through_level(level, lookback) =>
    if na(level)
        false
    else
        traveled = false
        for i = 1 to lookback
            if not na(high[i]) and not na(low[i])
                if low[i] <= level and high[i] >= level
                    traveled := true
                    break
        traveled

// Enhanced engulfing detection (following silver bullet logic)
var float lastUpLow = na      // low of last up-close candle
var float lastDownHigh = na   // high of last down-close candle

// Update last opposite-close extremes on every bar
if close > open
    lastUpLow := low
else if close < open
    lastDownHigh := high

// Bullish engulfing: green close above last down-candle's high
is_bullish_engulfing() =>
    close > open and not na(lastDownHigh) and close >= lastDownHigh

// Bearish engulfing: red close below last up-candle's low
is_bearish_engulfing() =>
    close < open and not na(lastUpLow) and close <= lastUpLow

is_doji() =>
    body_size = math.abs(close - open)
    candle_range = high - low
    candle_range > 0 and body_size / candle_range <= 0.1

is_hammer() =>
    body_size = math.abs(close - open)
    upper_shadow = high - math.max(close, open)
    lower_shadow = math.min(close, open) - low
    candle_range = high - low
    candle_range > 0 and lower_shadow >= 2 * body_size and upper_shadow <= body_size

// Function to check if any required candle pattern is present
candle_pattern_detected(signal_type) =>
    if not require_candle_patterns
        true
    else
        pattern_found = false
        if signal_type == "buy"
            if enable_engulfing and is_bullish_engulfing()
                pattern_found := true
            if enable_doji and is_doji()
                pattern_found := true
            if enable_hammer and is_hammer()
                pattern_found := true
        else if signal_type == "sell"
            if enable_engulfing and is_bearish_engulfing()
                pattern_found := true
            if enable_doji and is_doji()
                pattern_found := true
            if enable_hammer and is_hammer()
                pattern_found := true
        pattern_found

// Function to get all IB1 levels in an array
get_ib1_levels() =>
    levels = array.new<float>()
    if not na(ib1_high)
        array.push(levels, ib1_high)
    if not na(ib1_low)
        array.push(levels, ib1_low)
    if show_intermediate_levels and not na(ib1_mid)
        array.push(levels, ib1_mid)
    if show_extra_levels
        if not na(ib1_high_plus)
            array.push(levels, ib1_high_plus)
        if not na(ib1_low_minus)
            array.push(levels, ib1_low_minus)
        if not na(ib1_high_plus2)
            array.push(levels, ib1_high_plus2)
        if not na(ib1_low_minus2)
            array.push(levels, ib1_low_minus2)
        if show_intermediate_levels
            if not na(ib1_mid_plus)
                array.push(levels, ib1_mid_plus)
            if not na(ib1_mid_minus)
                array.push(levels, ib1_mid_minus)
    levels

// Function to get all IB2 levels in an array
get_ib2_levels() =>
    levels = array.new<float>()
    if not na(ib2_high)
        array.push(levels, ib2_high)
    if not na(ib2_low)
        array.push(levels, ib2_low)
    if show_intermediate_levels2 and not na(ib2_mid)
        array.push(levels, ib2_mid)
    if show_extra_levels2
        if not na(ib2_high_plus)
            array.push(levels, ib2_high_plus)
        if not na(ib2_low_minus)
            array.push(levels, ib2_low_minus)
        if not na(ib2_high_plus2)
            array.push(levels, ib2_high_plus2)
        if not na(ib2_low_minus2)
            array.push(levels, ib2_low_minus2)
        if show_intermediate_levels2
            if not na(ib2_mid_plus)
                array.push(levels, ib2_mid_plus)
            if not na(ib2_mid_minus)
                array.push(levels, ib2_mid_minus)
    levels

// Smart overlap detection - finds latest overlapping line
detect_smart_overlap() =>
    overlap_found = false
    latest_level = 0.0
    latest_bar = 0
    latest_source = ""
    overlap_level1 = 0.0
    overlap_level2 = 0.0

    if enable_ib1 and enable_ib2
        ib1_levels = get_ib1_levels()
        ib2_levels = get_ib2_levels()

        // Determine which IB set was created more recently
        ib1_is_newer = not na(ib1_high) and not na(ib2_high) and
                       (na(ib_delta2) or (not na(ib_delta) and bar_index > bar_index))

        if array.size(ib1_levels) > 0 and array.size(ib2_levels) > 0
            for i = 0 to array.size(ib1_levels) - 1
                level1 = array.get(ib1_levels, i)
                for j = 0 to array.size(ib2_levels) - 1
                    level2 = array.get(ib2_levels, j)
                    if levels_overlap(level1, level2)
                        overlap_found := true
                        overlap_level1 := level1
                        overlap_level2 := level2

                        // Use the level from the newer IB set as the "latest"
                        if ib1_is_newer
                            latest_level := level1
                            latest_source := "ib1"
                        else
                            latest_level := level2
                            latest_source := "ib2"
                        latest_bar := bar_index
                        break
                if overlap_found
                    break

    [overlap_found, latest_level, latest_bar, latest_source, overlap_level1, overlap_level2]

// Smart overlap detection
[overlap_found, latest_level, latest_bar, latest_source, overlap_level1, overlap_level2] = detect_smart_overlap()

// Smart box drawing (following silver bullet pattern with fixed height)
if enable_overlap_signals and show_overlap_boxes
    if overlap_found and (na(latest_overlap_level) or latest_level != latest_overlap_level or latest_bar != latest_overlap_bar)
        // Create new smart overlap box from the latest overlapping line
        box_top = latest_level + (box_height / 2)
        box_bottom = latest_level - (box_height / 2)

        // End previous box if active
        if not na(current_overlap_box)
            overlap_box_active := false

        // Create new box starting from the latest overlap detection
        current_overlap_box := box.new(bar_index, box_top, bar_index, box_bottom,
                                      border_color=overlap_box_color,
                                      bgcolor=color.new(overlap_box_color, box_transparency),
                                      border_width=2)
        array.push(overlap_boxes, current_overlap_box)
        overlap_box_active := true

        // Update tracking variables
        latest_overlap_level := latest_level
        latest_overlap_bar := latest_bar
        latest_overlap_source := latest_source

    else if overlap_found and overlap_box_active and not na(current_overlap_box)
        // Extend existing overlap box while overlap continues
        box.set_right(current_overlap_box, bar_index)
    else if not overlap_found and overlap_box_active
        // Stop extending when overlap ends
        overlap_box_active := false

// Enhanced signal generation with waiting mechanism (following silver bullet pattern)
buy_signal_triggered = false
sell_signal_triggered = false

// Check if price has traveled through overlapping levels
price_travel_confirmed = false
if require_price_travel and overlap_found
    // Check if price traveled through both overlapping levels
    level1_traveled = price_traveled_through_level(overlap_level1, lookback_bars)
    level2_traveled = price_traveled_through_level(overlap_level2, lookback_bars)
    price_travel_confirmed := level1_traveled and level2_traveled
else if not require_price_travel
    price_travel_confirmed := true

// Start waiting for signals when overlap + divergence + price travel conditions are met
if enable_overlap_signals and overlap_found and price_travel_confirmed
    // Start waiting for buy signal
    if (pos_reg_div_detected or pos_hid_div_detected) and not waitForBuySignal
        waitForBuySignal := true
        divergenceBar := bar_index
        barsSinceDivergence := 0

    // Start waiting for sell signal
    if (neg_reg_div_detected or neg_hid_div_detected) and not waitForSellSignal
        waitForSellSignal := true
        divergenceBar := bar_index
        barsSinceDivergence := 0

// Wait for engulfing patterns after divergence (up to 10 bars)
if waitForBuySignal
    barsSinceDivergence := bar_index - divergenceBar

    if barsSinceDivergence <= 10
        // Check for bullish engulfing or other patterns
        if candle_pattern_detected("buy")
            buy_label = label.new(bar_index, low - (high - low) * 0.1, "BUY",
                                 style=label.style_label_up, size=signal_label_size_value,
                                 color=buy_signal_color, textcolor=color.white)
            array.push(overlap_buy_signals, buy_label)
            buy_signal_triggered := true
            waitForBuySignal := false
    else
        // Stop waiting after 10 bars
        waitForBuySignal := false

if waitForSellSignal
    barsSinceDivergence := bar_index - divergenceBar

    if barsSinceDivergence <= 10
        // Check for bearish engulfing or other patterns
        if candle_pattern_detected("sell")
            sell_label = label.new(bar_index, high + (high - low) * 0.1, "SELL",
                                  style=label.style_label_down, size=signal_label_size_value,
                                  color=sell_signal_color, textcolor=color.white)
            array.push(overlap_sell_signals, sell_label)
            sell_signal_triggered := true
            waitForSellSignal := false
    else
        // Stop waiting after 10 bars
        waitForSellSignal := false

alertcondition(pos_reg_div_detected, title='Positive Regular Divergence Detected', message='Positive Regular Divergence Detected')
alertcondition(neg_reg_div_detected, title='Negative Regular Divergence Detected', message='Negative Regular Divergence Detected')
alertcondition(pos_hid_div_detected, title='Positive Hidden Divergence Detected', message='Positive Hidden Divergence Detected')
alertcondition(neg_hid_div_detected, title='Negative Hidden Divergence Detected', message='Negative Hidden Divergence Detected')

alertcondition(pos_reg_div_detected or pos_hid_div_detected, title='Positive Divergence Detected', message='Positive Divergence Detected')
alertcondition(neg_reg_div_detected or neg_hid_div_detected, title='Negative Divergence Detected', message='Negative Divergence Detected')

// Overlap + Divergence Signal Alerts
alertcondition(buy_signal_triggered, title='Buy Signal - Overlap + Bullish Divergence', message='Buy Signal: IB Levels Overlap + Bullish Divergence Detected')
alertcondition(sell_signal_triggered, title='Sell Signal - Overlap + Bearish Divergence', message='Sell Signal: IB Levels Overlap + Bearish Divergence Detected')

// Debug plots (uncomment to see condition status)
// plotchar(overlap_found, "Overlap", "●", location.top, color=color.yellow, size=size.tiny)
// plotchar(price_travel_confirmed, "Price Travel", "→", location.top, color=color.orange, size=size.tiny)
// plotchar(pos_reg_div_detected or pos_hid_div_detected, "Bull Div", "↑", location.bottom, color=color.green, size=size.tiny)
// plotchar(neg_reg_div_detected or neg_hid_div_detected, "Bear Div", "↓", location.top, color=color.red, size=size.tiny)
// plotchar(waitForBuySignal, "Wait Buy", "W", location.bottom, color=color.blue, size=size.tiny)
// plotchar(waitForSellSignal, "Wait Sell", "W", location.top, color=color.purple, size=size.tiny)
// plotchar(candle_pattern_detected("buy"), "Buy Pattern", "▲", location.bottom, color=color.lime, size=size.small)
// plotchar(candle_pattern_detected("sell"), "Sell Pattern", "▼", location.top, color=color.red, size=size.small)
// plotchar(buy_signal_triggered, "BUY SIGNAL", "B", location.bottom, color=color.lime, size=size.normal)
// plotchar(sell_signal_triggered, "SELL SIGNAL", "S", location.top, color=color.red, size=size.normal)
