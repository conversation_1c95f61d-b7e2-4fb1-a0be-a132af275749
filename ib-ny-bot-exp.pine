//@version=5

// indicator("Divergence for Many Indicators v5", overlay = true, max_bars_back = 1000, max_lines_count = 400, max_labels_count = 400)
indicator("Initial Balance", shorttitle="IB", overlay=true, max_bars_back=5000,max_lines_count = 500, max_labels_count = 500,max_boxes_count = 500,max_polylines_count = 100)

// = ENABLE/DISABLE INDICATORS =
enable_ib = input.bool(true, "Enable Initial Balance", group="Enable/Disable Indicators")
enable_divergence = input.bool(true, "Enable Divergence", group="Enable/Disable Indicators")
enable_att = input.bool(true, "Enable ATT", group="Enable/Disable Indicators")
enable_vwcb = input.bool(true, "Enable Volume Weighted Colored Bars", group="Enable/Disable Indicators")

// = INPUTS =
ib_ib_session               = input.session("0820-0920", title="Calculation period for the initial balance", group="Calculation period")
ib_show_extra_levels        = input.bool(true,  "Show extra levels (IBH x2 & IBL x2)",                 group="Information")
ib_show_intermediate_levels = input.bool(true,  "Show intermediate levels (50%)",                      group="Information")
ib_show_ib_calculation_area = input.bool(true,  "Initial balance calculation period coloration",      group="Information")
ib_fill_ib_areas            = input.bool(true,  "Colour IB areas",                                    group="Information")
ib_only_current_levels      = input.bool(false, "Only display the current IB Levels",                 group="Information")
ib_only_current_zone        = input.bool(false, "Only display the current IB calculation area",      group="Information")

// Label Settings
ib_group_label = "Label Settings"
ib_show_labels = input.bool(true, "Show Labels", group=ib_group_label)
ib_show_price_in_label = input.bool(true, "Show Price in Labels", group=ib_group_label)
ib_label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=ib_group_label)
ib_label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=ib_group_label)
ib_label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=ib_group_label)

// = DRAWING PARAMETERS =
ib_lvl_width         = input.int(1,           "Daily price level width",                               group="Drawings")
ib_high_col          = input.color(color.green,   "Initial balance high levels color",                group="Drawings")
ib_low_col           = input.color(color.red,     "Initial balance low levels color",                 group="Drawings")
ib_middle_col        = input.color(#ffa726,       "50% initial balance color",                        group="Drawings")
ib_main_levels_style = input.string("Solid",  "Main levels line style",     options=["Solid","Dashed","Dotted"], group="Drawings")
ib_ext_levels_style  = input.string("Dashed", "Extended levels line style", options=["Solid","Dashed","Dotted"], group="Drawings")
ib_int_levels_style  = input.string("Dotted", "Intermediate levels line style", options=["Solid","Dotted","Dashed"], group="Drawings")
ib_fill_ib_color     = input.color(#b8851faa, "IB area background color",                         group="Drawings")
ib_ext = extend.none  // manage extension manually

// = HELPER FUNCTIONS =
ib_get_line_style(ib_styleStr) =>
    ib_styleStr == "Solid" ? line.style_solid : ib_styleStr == "Dashed" ? line.style_dashed : line.style_dotted

ib_get_label_size(ib_sizeStr) =>
    ib_result = switch ib_sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    ib_result

// = UTILITIES =
ib_inSession(ib_sess) => na(time(timeframe.period, ib_sess)) == false

ib_get_levels(ib_n) =>
    float ib_h = high[1]
    float ib_l = low
    for ib_i = 1 to ib_n
        if low[ib_i]  < ib_l
            ib_l := low[ib_i]
        if high[ib_i] > ib_h
            ib_h := high[ib_i]
    [ib_h, ib_l, (ib_h + ib_l) / 2]

// Function to manage line arrays based on max_periods
ib_max_periods = 20
ib_manage_line_history(ib_line_array) =>
    if array.size(ib_line_array) >= ib_max_periods
        ib_oldest_line = array.get(ib_line_array, array.size(ib_line_array) - 1)
        if not na(ib_oldest_line)
            line.delete(ib_oldest_line)
        array.pop(ib_line_array)

// Function to delete all labels in an array
ib_delete_all_labels(ib_label_array) =>
    if array.size(ib_label_array) > 0
        for ib_i = 0 to array.size(ib_label_array) - 1
            ib_label_to_delete = array.get(ib_label_array, ib_i)
            if not na(ib_label_to_delete)
                label.delete(ib_label_to_delete)
        array.clear(ib_label_array)

// = STATE VARIABLES =
var ib_line_style_value = ib_get_line_style(ib_main_levels_style)
var ib_label_size_value = ib_get_label_size(ib_label_size)

// = SESSION STATE =
var float ib_ib_delta       = na
var int   ib_offset         = 0
var float[] ib_delta_history = array.new_float(20)
ib_ins = ib_inSession(ib_ib_session)
bgcolor(enable_ib and ib_show_ib_calculation_area and ib_ins ? #673ab730 : na, title="IB calculation zone")

// = LINE AND LABEL ARRAYS =
var array<line> ib_ibh_lines = array.new<line>()
var array<line> ib_ibl_lines = array.new<line>()
var array<line> ib_ibm_lines = array.new<line>()
var array<line> ib_ib_plus_lines = array.new<line>()
var array<line> ib_ib_minus_lines = array.new<line>()
var array<line> ib_ib_plus2_lines = array.new<line>()
var array<line> ib_ib_minus2_lines = array.new<line>()
var array<line> ib_ibm_plus_lines = array.new<line>()
var array<line> ib_ibm_minus_lines = array.new<line>()

var array<label> ib_ibh_labels = array.new<label>()
var array<label> ib_ibl_labels = array.new<label>()
var array<label> ib_ibm_labels = array.new<label>()
var array<label> ib_ib_plus_labels = array.new<label>()
var array<label> ib_ib_minus_labels = array.new<label>()
var array<label> ib_ib_plus2_labels = array.new<label>()
var array<label> ib_ib_minus2_labels = array.new<label>()
var array<label> ib_ibm_plus_labels = array.new<label>()
var array<label> ib_ibm_minus_labels = array.new<label>()

// = CURRENT LINES AND LABELS =
var line  ib_ibh_line        = na
var line  ib_ibl_line        = na
var line  ib_ibm_line        = na
var line  ib_ib_plus_line    = na
var line  ib_ib_minus_line   = na
var line  ib_ib_plus2_line   = na
var line  ib_ib_minus2_line  = na
var line  ib_ibm_plus_line   = na
var line  ib_ibm_minus_line  = na
var box   ib_ib_area         = na

var label ib_ibh_label       = na
var label ib_ibl_label       = na
var label ib_ibm_label       = na
var label ib_ib_plus_label   = na
var label ib_ib_minus_label  = na
var label ib_ib_plus2_label  = na
var label ib_ib_minus2_label = na
var label ib_ibm_plus_label  = na
var label ib_ibm_minus_label = na

// = BUILD SESSION =
if ib_ins
    ib_offset += 1

if enable_ib and ib_ins[1] and not ib_ins
    // calculate levels
    [ib_h, ib_l, ib_m] = ib_get_levels(ib_offset)
    ib_ib_delta := ib_h - ib_l

    // update history
    if array.size(ib_delta_history) >= 20
        array.shift(ib_delta_history)
    array.push(ib_delta_history, ib_ib_delta)

    // Delete old labels when a new session starts
    if ib_show_labels
        ib_delete_all_labels(ib_ibh_labels)
        ib_delete_all_labels(ib_ibl_labels)
        ib_delete_all_labels(ib_ibm_labels)
        ib_delete_all_labels(ib_ib_plus_labels)
        ib_delete_all_labels(ib_ib_minus_labels)
        ib_delete_all_labels(ib_ib_plus2_labels)
        ib_delete_all_labels(ib_ib_minus2_labels)
        ib_delete_all_labels(ib_ibm_plus_labels)
        ib_delete_all_labels(ib_ibm_minus_labels)

    // Create main level lines
    ib_ibh_line := line.new(bar_index, ib_h, bar_index, ib_h, color=ib_high_col, width=ib_lvl_width, style=ib_line_style_value)
    array.unshift(ib_ibh_lines, ib_ibh_line)
    ib_manage_line_history(ib_ibh_lines)

    ib_ibl_line := line.new(bar_index, ib_l, bar_index, ib_l, color=ib_low_col, width=ib_lvl_width, style=ib_line_style_value)
    array.unshift(ib_ibl_lines, ib_ibl_line)
    ib_manage_line_history(ib_ibl_lines)

    // Main level labels
    if ib_show_labels
        ib_ibh_label := label.new(bar_index + ib_label_x_offset_bars, ib_h + ib_label_y_offset, "IBH" + (ib_show_price_in_label ? str.format(" ({0})", ib_h) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_high_col, color=color.new(color.black, 100))
        array.push(ib_ibh_labels, ib_ibh_label)

        ib_ibl_label := label.new(bar_index + ib_label_x_offset_bars, ib_l + ib_label_y_offset, "IBL" + (ib_show_price_in_label ? str.format(" ({0})", ib_l) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_low_col, color=color.new(color.black, 100))
        array.push(ib_ibl_labels, ib_ibl_label)

    // intermediate 50% line
    if ib_show_intermediate_levels
        ib_ibm_line := line.new(bar_index, ib_m, bar_index, ib_m, color=ib_middle_col, width=ib_lvl_width, style=ib_get_line_style(ib_int_levels_style))
        array.unshift(ib_ibm_lines, ib_ibm_line)
        ib_manage_line_history(ib_ibm_lines)

        if ib_show_labels
            ib_ibm_label := label.new(bar_index + ib_label_x_offset_bars, ib_m + ib_label_y_offset, "IBM" + (ib_show_price_in_label ? str.format(" ({0})", ib_m) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_middle_col, color=color.new(color.black, 100))
            array.push(ib_ibm_labels, ib_ibm_label)

    // extra levels
    if ib_show_extra_levels
        ib_ib_plus_line := line.new(bar_index, ib_h + ib_ib_delta, bar_index, ib_h + ib_ib_delta, color=ib_high_col, width=ib_lvl_width, style=ib_get_line_style(ib_ext_levels_style))
        array.unshift(ib_ib_plus_lines, ib_ib_plus_line)
        ib_manage_line_history(ib_ib_plus_lines)

        ib_ib_minus_line := line.new(bar_index, ib_l - ib_ib_delta, bar_index, ib_l - ib_ib_delta, color=ib_low_col, width=ib_lvl_width, style=ib_get_line_style(ib_ext_levels_style))
        array.unshift(ib_ib_minus_lines, ib_ib_minus_line)
        ib_manage_line_history(ib_ib_minus_lines)

        ib_ib_plus2_line := line.new(bar_index, ib_h + ib_ib_delta * 2, bar_index, ib_h + ib_ib_delta * 2, color=ib_high_col, width=ib_lvl_width, style=ib_get_line_style(ib_ext_levels_style))
        array.unshift(ib_ib_plus2_lines, ib_ib_plus2_line)
        ib_manage_line_history(ib_ib_plus2_lines)

        ib_ib_minus2_line := line.new(bar_index, ib_l - ib_ib_delta * 2, bar_index, ib_l - ib_ib_delta * 2, color=ib_low_col, width=ib_lvl_width, style=ib_get_line_style(ib_ext_levels_style))
        array.unshift(ib_ib_minus2_lines, ib_ib_minus2_line)
        ib_manage_line_history(ib_ib_minus2_lines)

        if ib_show_labels
            ib_ib_plus_label := label.new(bar_index + ib_label_x_offset_bars, (ib_h + ib_ib_delta) + ib_label_y_offset, "IBH+Δ" + (ib_show_price_in_label ? str.format(" ({0})", ib_h + ib_ib_delta) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_high_col, color=color.new(color.black, 100))
            array.push(ib_ib_plus_labels, ib_ib_plus_label)

            ib_ib_minus_label := label.new(bar_index + ib_label_x_offset_bars, (ib_l - ib_ib_delta) + ib_label_y_offset, "IBL-Δ" + (ib_show_price_in_label ? str.format(" ({0})", ib_l - ib_ib_delta) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_low_col, color=color.new(color.black, 100))
            array.push(ib_ib_minus_labels, ib_ib_minus_label)

            ib_ib_plus2_label := label.new(bar_index + ib_label_x_offset_bars, (ib_h + ib_ib_delta * 2) + ib_label_y_offset, "IBH+2Δ" + (ib_show_price_in_label ? str.format(" ({0})", ib_h + ib_ib_delta * 2) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_high_col, color=color.new(color.black, 100))
            array.push(ib_ib_plus2_labels, ib_ib_plus2_label)

            ib_ib_minus2_label := label.new(bar_index + ib_label_x_offset_bars, (ib_l - ib_ib_delta * 2) + ib_label_y_offset, "IBL-2Δ" + (ib_show_price_in_label ? str.format(" ({0})", ib_l - ib_ib_delta * 2) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_low_col, color=color.new(color.black, 100))
            array.push(ib_ib_minus2_labels, ib_ib_minus2_label)

        if ib_show_intermediate_levels
            ib_ibm_plus_line := line.new(bar_index, ib_h + ib_ib_delta / 2, bar_index, ib_h + ib_ib_delta / 2, color=ib_middle_col, width=ib_lvl_width, style=ib_get_line_style(ib_int_levels_style))
            array.unshift(ib_ibm_plus_lines, ib_ibm_plus_line)
            ib_manage_line_history(ib_ibm_plus_lines)

            ib_ibm_minus_line := line.new(bar_index, ib_l - ib_ib_delta / 2, bar_index, ib_l - ib_ib_delta / 2, color=ib_middle_col, width=ib_lvl_width, style=ib_get_line_style(ib_int_levels_style))
            array.unshift(ib_ibm_minus_lines, ib_ibm_minus_line)
            ib_manage_line_history(ib_ibm_minus_lines)

            if ib_show_labels
                ib_ibm_plus_label := label.new(bar_index + ib_label_x_offset_bars, (ib_h + ib_ib_delta / 2) + ib_label_y_offset, "IBM+Δ/2" + (ib_show_price_in_label ? str.format(" ({0})", ib_h + ib_ib_delta / 2) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_middle_col, color=color.new(color.black, 100))
                array.push(ib_ibm_plus_labels, ib_ibm_plus_label)

                ib_ibm_minus_label := label.new(bar_index + ib_label_x_offset_bars, (ib_l - ib_ib_delta / 2) + ib_label_y_offset, "IBM-Δ/2" + (ib_show_price_in_label ? str.format(" ({0})", ib_l - ib_ib_delta / 2) : ""), style=label.style_label_left, size=ib_label_size_value, textcolor=ib_middle_col, color=color.new(color.black, 100))
                array.push(ib_ibm_minus_labels, ib_ibm_minus_label)

    // fill the IB area
    if ib_fill_ib_areas
        ib_ib_area := box.new(bar_index[ib_offset], ib_h, bar_index, ib_l, bgcolor=ib_fill_ib_color, border_color=color.new(color.black, 100))

    ib_offset := 0

// = UPDATE LINES DURING THE CURRENT DAY =
if enable_ib and not ib_ins and not na(ib_ibh_line)
    // Update main level lines
    line.set_x2(ib_ibh_line, bar_index)
    line.set_x2(ib_ibl_line, bar_index)

    if ib_show_labels
        label.set_x(ib_ibh_label, bar_index + ib_label_x_offset_bars)
        label.set_x(ib_ibl_label, bar_index + ib_label_x_offset_bars)

    if ib_show_intermediate_levels and not na(ib_ibm_line)
        line.set_x2(ib_ibm_line, bar_index)
        if ib_show_labels
            label.set_x(ib_ibm_label, bar_index + ib_label_x_offset_bars)

    if ib_show_extra_levels
        if not na(ib_ib_plus_line)
            line.set_x2(ib_ib_plus_line, bar_index)
        if not na(ib_ib_minus_line)
            line.set_x2(ib_ib_minus_line, bar_index)
        if not na(ib_ib_plus2_line)
            line.set_x2(ib_ib_plus2_line, bar_index)
        if not na(ib_ib_minus2_line)
            line.set_x2(ib_ib_minus2_line, bar_index)

        if ib_show_labels
            if not na(ib_ib_plus_label)
                label.set_x(ib_ib_plus_label, bar_index + ib_label_x_offset_bars)
            if not na(ib_ib_minus_label)
                label.set_x(ib_ib_minus_label, bar_index + ib_label_x_offset_bars)
            if not na(ib_ib_plus2_label)
                label.set_x(ib_ib_plus2_label, bar_index + ib_label_x_offset_bars)
            if not na(ib_ib_minus2_label)
                label.set_x(ib_ib_minus2_label, bar_index + ib_label_x_offset_bars)

        if ib_show_intermediate_levels
            if not na(ib_ibm_plus_line)
                line.set_x2(ib_ibm_plus_line, bar_index)
            if not na(ib_ibm_minus_line)
                line.set_x2(ib_ibm_minus_line, bar_index)

            if ib_show_labels
                if not na(ib_ibm_plus_label)
                    label.set_x(ib_ibm_plus_label, bar_index + ib_label_x_offset_bars)
                if not na(ib_ibm_minus_label)
                    label.set_x(ib_ibm_minus_label, bar_index + ib_label_x_offset_bars)





//indicator-divergence

//@version=5
// indicator("Divergence for Many Indicators v5", overlay = true, max_bars_back = 1000, max_lines_count = 400, max_labels_count = 400)
prd = input.int(defval = 5, title = "Pivot Period", minval = 1, maxval = 50)
source = input.string(defval = "Close", title = "Source for Pivot Points", options = ["Close", "High/Low"])
searchdiv = input.string(defval = "Regular", title = "Divergence Type", options = ["Regular", "Hidden", "Regular/Hidden"])
showindis = input.string(defval = "Don't Show", title = "Show Indicator Names", options = ["Full", "First Letter", "Don't Show"])
showlimit = input.int(1, title="Minimum Number of Divergence", minval = 1, maxval = 11)
maxpp = input.int(defval = 10, title = "Maximum Pivot Points to Check", minval = 1, maxval = 20)
maxbars = input.int(defval = 100, title = "Maximum Bars to Check", minval = 30, maxval = 200)
shownum = input.bool(defval = true, title = "Show Divergence Number")
showlast = input.bool(defval = false, title = "Show Only Last Divergence")
dontconfirm = input.bool(defval = false, title = "Don't Wait for Confirmation")
showlines = input.bool(defval = false, title = "Show Divergence Lines")
showpivot = input.bool(defval = false, title = "Show Pivot Points")
calcmacd = input.bool(defval = true, title = "MACD")
calcmacda = input.bool(defval = true, title = "MACD Histogram")
calcrsi = input.bool(defval = true, title = "RSI")
calcstoc = input.bool(defval = true, title = "Stochastic")
calccci = input.bool(defval = true, title = "CCI")
calcmom = input.bool(defval = true, title = "Momentum")
calcobv = input.bool(defval = true, title = "OBV")
calcvwmacd = input.bool(true, title = "VWmacd")
calccmf = input.bool(true, title = "Chaikin Money Flow")
calcmfi = input.bool(true, title = "Money Flow Index")
calcext = input.bool(false, title = "Check External Indicator")
externalindi = input.source(defval = close, title = "External Indicator")
pos_reg_div_col = input.color(defval = color.yellow, title = "Positive Regular Divergence")
neg_reg_div_col = input.color(defval = color.navy, title = "Negative Regular Divergence")
pos_hid_div_col = input.color(defval = color.lime, title = "Positive Hidden Divergence")
neg_hid_div_col = input.color(defval = color.red, title = "Negative Hidden Divergence")
pos_div_text_col = input.color(defval = color.black, title = "Positive Divergence Text Color")
neg_div_text_col = input.color(defval = color.white, title = "Negative Divergence Text Color")
reg_div_l_style_ = input.string(defval = "Solid", title = "Regular Divergence Line Style", options = ["Solid", "Dashed", "Dotted"])
hid_div_l_style_ = input.string(defval = "Dashed", title = "Hidden Divergence Line Style", options = ["Solid", "Dashed", "Dotted"])
reg_div_l_width = input.int(defval = 2, title = "Regular Divergence Line Width", minval = 1, maxval = 5)
hid_div_l_width = input.int(defval = 1, title = "Hidden Divergence Line Width", minval = 1, maxval = 5)
showmas = input.bool(defval = false, title = "Show MAs 50 & 200", inline = "ma12")
cma1col = input.color(defval = color.lime, title = "", inline = "ma12")
cma2col = input.color(defval = color.red, title = "", inline = "ma12")

plot(enable_divergence and showmas ? ta.sma(close, 50) : na, color = enable_divergence and showmas ? cma1col : na)
plot(enable_divergence and showmas ? ta.sma(close, 200) : na, color = enable_divergence and showmas ? cma2col: na)

// set line styles
var reg_div_l_style = reg_div_l_style_ == "Solid" ? line.style_solid :
                       reg_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted
var hid_div_l_style = hid_div_l_style_ == "Solid" ? line.style_solid :
                       hid_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted


// get indicators
rsi = ta.rsi(close, 14) // RSI
[macd, signal, deltamacd] = ta.macd(close, 12, 26, 9) // MACD
moment = ta.mom(close, 10) // Momentum
cci = ta.cci(close, 10) // CCI
Obv = ta.obv // OBV
stk = ta.sma(ta.stoch(close, high, low, 14), 3) // Stoch
maFast = ta.vwma(close, 12), maSlow = ta.vwma(close, 26), vwmacd = maFast - maSlow // volume weighted macd
Cmfm = ((close-low) - (high-close)) / (high - low), Cmfv = Cmfm * volume, cmf = ta.sma(Cmfv, 21) / ta.sma(volume,21) // Chaikin money flow
Mfi = ta.mfi(close, 14) // Money Flow Index

// keep indicators names and colors in arrays
var indicators_name = array.new_string(11)
var div_colors = array.new_color(4)
if barstate.isfirst
    // names
    array.set(indicators_name, 0, showindis == "Full" ? "MACD" : "M")
    array.set(indicators_name, 1, showindis == "Full" ? "Hist" : "H")
    array.set(indicators_name, 2, showindis == "Full" ? "RSI" : "E")
    array.set(indicators_name, 3, showindis == "Full" ? "Stoch" : "S")
    array.set(indicators_name, 4, showindis == "Full" ? "CCI" : "C")
    array.set(indicators_name, 5, showindis == "Full" ? "MOM" : "M")
    array.set(indicators_name, 6, showindis == "Full" ? "OBV" : "O")
    array.set(indicators_name, 7, showindis == "Full" ? "VWMACD" : "V")
    array.set(indicators_name, 8, showindis == "Full" ? "CMF" : "C")
    array.set(indicators_name, 9, showindis == "Full" ? "MFI" : "M")
    array.set(indicators_name,10, showindis == "Full" ? "Extrn" : "X")
    //colors
    array.set(div_colors, 0, pos_reg_div_col)
    array.set(div_colors, 1, neg_reg_div_col)
    array.set(div_colors, 2, pos_hid_div_col)
    array.set(div_colors, 3, neg_hid_div_col)

// Check if we get new Pivot High Or Pivot Low
float ph = ta.pivothigh((source == "Close" ? close : high), prd, prd)
float pl = ta.pivotlow((source == "Close" ? close : low), prd, prd)
plotshape(enable_divergence and ph and showpivot, text = "H",  style = shape.labeldown, color = color.new(color.white, 100), textcolor = color.red, location = location.abovebar, offset = -prd)
plotshape(enable_divergence and pl and showpivot, text = "L",  style = shape.labelup, color = color.new(color.white, 100), textcolor = color.lime, location = location.belowbar, offset = -prd)

// keep values and positions of Pivot Highs/Lows in the arrays
var int maxarraysize = 20
var ph_positions = array.new_int(maxarraysize, 0)
var pl_positions = array.new_int(maxarraysize, 0)
var ph_vals = array.new_float(maxarraysize, 0.)
var pl_vals = array.new_float(maxarraysize, 0.)

// add PHs to the array
if ph
    array.unshift(ph_positions, bar_index)
    array.unshift(ph_vals, ph)
    if array.size(ph_positions) > maxarraysize
        array.pop(ph_positions)
        array.pop(ph_vals)

// add PLs to the array
if pl
    array.unshift(pl_positions, bar_index)
    array.unshift(pl_vals, pl)
    if array.size(pl_positions) > maxarraysize
        array.pop(pl_positions)
        array.pop(pl_vals)

// functions to check Regular Divergences and Hidden Divergences

// function to check positive regular or negative hidden divergence
// cond == 1 => positive_regular, cond == 2=> negative_hidden
positive_regular_positive_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : low
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src > src[1] or close > close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(pl_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(pl_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(pl_vals, x))) or
               (cond == 2 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(pl_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] < virtual_line1 or close[y] < virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// function to check negative regular or positive hidden divergence
// cond == 1 => negative_regular, cond == 2=> positive_hidden
negative_regular_negative_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : high
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src < src[1] or close < close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(ph_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(ph_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(ph_vals, x))) or
               (cond == 2 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(ph_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] > virtual_line1 or close[y] > virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// calculate 4 types of divergence if enabled in the options and return divergences in an array
calculate_divs(cond, indicator)=>
    divs = array.new_int(4, 0)
    array.set(divs, 0, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? positive_regular_positive_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 1, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? negative_regular_negative_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 2, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? positive_regular_positive_hidden_divergence(indicator, 2) : 0)
    array.set(divs, 3, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? negative_regular_negative_hidden_divergence(indicator, 2) : 0)
    divs

// array to keep all divergences
var all_divergences = array.new_int(44) // 11 indicators * 4 divergence = 44 elements
// set related array elements
array_set_divs(div_pointer, index)=>
    for x = 0 to 3
        array.set(all_divergences, index * 4 + x, array.get(div_pointer, x))

// set divergences array
if enable_divergence
    array_set_divs(calculate_divs(calcmacd, macd), 0)
    array_set_divs(calculate_divs(calcmacda, deltamacd), 1)
    array_set_divs(calculate_divs(calcrsi, rsi), 2)
    array_set_divs(calculate_divs(calcstoc, stk), 3)
    array_set_divs(calculate_divs(calccci, cci), 4)
    array_set_divs(calculate_divs(calcmom, moment), 5)
    array_set_divs(calculate_divs(calcobv, Obv), 6)
    array_set_divs(calculate_divs(calcvwmacd, vwmacd), 7)
    array_set_divs(calculate_divs(calccmf, cmf), 8)
    array_set_divs(calculate_divs(calcmfi, Mfi), 9)
    array_set_divs(calculate_divs(calcext, externalindi), 10)

// check minimum number of divergence, if less than showlimit then delete all divergence
if enable_divergence
    total_div = 0
    for x = 0 to array.size(all_divergences) - 1
        total_div := total_div + math.round(math.sign(array.get(all_divergences, x)))

    if total_div < showlimit
        array.fill(all_divergences, 0)

// keep line in an array
var pos_div_lines = array.new_line(0)
var neg_div_lines = array.new_line(0)
var pos_div_labels = array.new_label(0)
var neg_div_labels = array.new_label(0)

// remove old lines and labels if showlast option is enabled
delete_old_pos_div_lines()=>
    if array.size(pos_div_lines) > 0
        for j = 0 to array.size(pos_div_lines) - 1
            line.delete(array.get(pos_div_lines, j))
        array.clear(pos_div_lines)

delete_old_neg_div_lines()=>
    if array.size(neg_div_lines) > 0
        for j = 0 to array.size(neg_div_lines) - 1
            line.delete(array.get(neg_div_lines, j))
        array.clear(neg_div_lines)

delete_old_pos_div_labels()=>
    if array.size(pos_div_labels) > 0
        for j = 0 to array.size(pos_div_labels) - 1
            label.delete(array.get(pos_div_labels, j))
        array.clear(pos_div_labels)

delete_old_neg_div_labels()=>
    if array.size(neg_div_labels) > 0
        for j = 0 to array.size(neg_div_labels) - 1
            label.delete(array.get(neg_div_labels, j))
        array.clear(neg_div_labels)

// delete last creted lines and labels until we met new PH/PV
delete_last_pos_div_lines_label(n)=>
    if n > 0 and array.size(pos_div_lines) >= n
        asz = array.size(pos_div_lines)
        for j = 1 to n
            line.delete(array.get(pos_div_lines, asz - j))
            array.pop(pos_div_lines)
        if array.size(pos_div_labels) > 0
            label.delete(array.get(pos_div_labels, array.size(pos_div_labels) - 1))
            array.pop(pos_div_labels)

delete_last_neg_div_lines_label(n)=>
    if n > 0 and array.size(neg_div_lines) >= n
        asz = array.size(neg_div_lines)
        for j = 1 to n
            line.delete(array.get(neg_div_lines, asz - j))
            array.pop(neg_div_lines)
        if array.size(neg_div_labels) > 0
            label.delete(array.get(neg_div_labels, array.size(neg_div_labels) - 1))
            array.pop(neg_div_labels)

// variables for Alerts
pos_reg_div_detected = false
neg_reg_div_detected = false
pos_hid_div_detected = false
neg_hid_div_detected = false

// to remove lines/labels until we met new // PH/PL
var last_pos_div_lines = 0
var last_neg_div_lines = 0
var remove_last_pos_divs = false
var remove_last_neg_divs = false
if pl
    remove_last_pos_divs := false
    last_pos_div_lines := 0
if ph
    remove_last_neg_divs := false
    last_neg_div_lines := 0

// draw divergences lines and labels
divergence_text_top = ""
divergence_text_bottom = ""
distances = array.new_int(0)
dnumdiv_top = 0
dnumdiv_bottom = 0
top_label_col = color.white
bottom_label_col = color.white
old_pos_divs_can_be_removed = true
old_neg_divs_can_be_removed = true
startpoint = dontconfirm ? 0 : 1 // used for don't confirm option

if enable_divergence
    for x = 0 to 10
        div_type = -1
        for y = 0 to 3
            if array.get(all_divergences, x * 4 + y) > 0 // any divergence?
            div_type := y
            if (y % 2) == 1
                dnumdiv_top := dnumdiv_top + 1
                top_label_col := array.get(div_colors, y)
            if (y % 2) == 0
                dnumdiv_bottom := dnumdiv_bottom + 1
                bottom_label_col := array.get(div_colors, y)
            if not array.includes(distances, array.get(all_divergences, x * 4 + y))  // line not exist ?
                array.push(distances, array.get(all_divergences, x * 4 + y))
                new_line = showlines ? line.new(x1 = bar_index - array.get(all_divergences, x * 4 + y),
                          y1 = (source == "Close" ? close[array.get(all_divergences, x * 4 + y)] :
                                           (y % 2) == 0 ? low[array.get(all_divergences, x * 4 + y)] :
                                                          high[array.get(all_divergences, x * 4 + y)]),
                          x2 = bar_index - startpoint,
                          y2 = (source == "Close" ? close[startpoint] :
                                           (y % 2) == 0 ? low[startpoint] :
                                                          high[startpoint]),
                          color = array.get(div_colors, y),
                          style = y < 2 ? reg_div_l_style : hid_div_l_style,
                          width = y < 2 ? reg_div_l_width : hid_div_l_width
                          )
                          : na
                if (y % 2) == 0
                    if old_pos_divs_can_be_removed
                        old_pos_divs_can_be_removed := false
                        if not showlast and remove_last_pos_divs
                            delete_last_pos_div_lines_label(last_pos_div_lines)
                            last_pos_div_lines := 0
                        if showlast
                            delete_old_pos_div_lines()
                    array.push(pos_div_lines, new_line)
                    last_pos_div_lines := last_pos_div_lines + 1
                    remove_last_pos_divs := true

                if (y % 2) == 1
                    if old_neg_divs_can_be_removed
                        old_neg_divs_can_be_removed := false
                        if not showlast and remove_last_neg_divs
                            delete_last_neg_div_lines_label(last_neg_div_lines)
                            last_neg_div_lines := 0
                        if showlast
                            delete_old_neg_div_lines()
                    array.push(neg_div_lines, new_line)
                    last_neg_div_lines := last_neg_div_lines + 1
                    remove_last_neg_divs := true

            // set variables for alerts
            if y == 0
                pos_reg_div_detected := true
            if y == 1
                neg_reg_div_detected := true
            if y == 2
                pos_hid_div_detected := true
            if y == 3
                neg_hid_div_detected := true
    // get text for labels
    if div_type >= 0
        divergence_text_top    := divergence_text_top    + ((div_type % 2) == 1 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")
        divergence_text_bottom := divergence_text_bottom + ((div_type % 2) == 0 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")


// draw labels
if showindis != "Don't Show" or shownum
    if shownum and dnumdiv_top > 0
        divergence_text_top := divergence_text_top + str.tostring(dnumdiv_top)
    if shownum and dnumdiv_bottom > 0
        divergence_text_bottom := divergence_text_bottom + str.tostring(dnumdiv_bottom)
    if divergence_text_top != ""
        if showlast
            delete_old_neg_div_labels()
        array.push(neg_div_labels,
                      label.new( x = bar_index,
                                 y = math.max(high, high[1]),
                                 text = divergence_text_top,
                                 color = top_label_col,
                                 textcolor = neg_div_text_col,
                                 style = label.style_label_down
                                 ))

    if divergence_text_bottom != ""
        if showlast
            delete_old_pos_div_labels()
        array.push(pos_div_labels,
                      label.new( x = bar_index,
                                 y = math.min(low, low[1]),
                                 text = divergence_text_bottom,
                                 color = bottom_label_col,
                                 textcolor = pos_div_text_col,
                                 style = label.style_label_up
                                 ))

    // Alert conditions for divergence
    alertcondition(enable_divergence and pos_reg_div_detected, title='Positive Regular Divergence Detected', message='Positive Regular Divergence Detected')
    alertcondition(enable_divergence and neg_reg_div_detected, title='Negative Regular Divergence Detected', message='Negative Regular Divergence Detected')
    alertcondition(enable_divergence and pos_hid_div_detected, title='Positive Hidden Divergence Detected', message='Positive Hidden Divergence Detected')
    alertcondition(enable_divergence and neg_hid_div_detected, title='Negative Hidden Divergence Detected', message='Negative Hidden Divergence Detected')

    alertcondition(enable_divergence and (pos_reg_div_detected or pos_hid_div_detected), title='Positive Divergence Detected', message='Positive Divergence Detected')
    alertcondition(enable_divergence and (neg_reg_div_detected or neg_hid_div_detected), title='Negative Divergence Detected', message='Negative Divergence Detected')





//-----att indicator-----
// HTF Box Settings
att_group_candle = 'HTF Box Settings'

// HTF Box 1
att_htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP1', group=att_group_candle)
att_htfUser1  = input.string('1 Hour', 'TF1', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP1', group=att_group_candle)
att_bullC1    = input.color(#00000000, 'Bull1', inline='COL1', group=att_group_candle)
att_bearC1    = input.color(#00000000, 'Bear1', inline='COL1', group=att_group_candle)
att_showBoxes = input.bool(false, 'Show HTF Boxes', group=att_group_candle) // New input for toggling boxes

// Common Settings
att_group_common = 'Common Settings'
att_trans        = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=att_group_common)
att_lw           = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=att_group_common)
att_showNumbers  = input.bool(false, 'Show Candle Numbers', group=att_group_common)
att_numbersColor = input.color(color.white, 'Numbers Color', inline='NUM', group=att_group_common)
att_numbersSize  = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM', group=att_group_common)

// ATT Circle Settings
att_group_att = 'ATT Circle Settings'
att_showATT      = input.bool(true, 'Show ATT Circles', group=att_group_att)
att_showATTNumbers = input.bool(true, 'Show ATT Numbers on Circles', group=att_group_att)
att_showHistoricalATT = input.bool(true, 'Show Historical ATT Circles', group=att_group_att)
att_attColor1    = input.color(#8ee60ac4, 'HTF1 Color', inline='ATT_COL1', group=att_group_att)

// ATT Prediction Settings
att_group_prediction = 'ATT Prediction Settings'
att_showPrediction = input.bool(true, 'Show ATT Predictions', group=att_group_prediction)
att_predictionDistance = input.int(5, 'Show predictions X candles ahead', minval=1, maxval=20, group=att_group_prediction)
att_predictionColor = input.color(color.yellow, 'Prediction Label Color', group=att_group_prediction)
att_predictionSize = input.string(size.tiny, 'Prediction Label Size', options=[size.tiny, size.small, size.normal], group=att_group_prediction)

// The ATT candle numbers where arrow marks will be drawn
var att_att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

att_checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 : _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)
    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

att_f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0., htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.
    var int candleCount = 0

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 0
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        candleCount += 1
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c, candleCount]

att_f_getTF(_htf) =>
    _htf == '3 Mins' ? '3' :_htf == '5 Mins' ? '5' :_htf == '10 Mins' ? '10' :_htf == '15 Mins' ? '15' :_htf == '30 Mins' ? '30' :_htf == '45 Mins' ? '45' :_htf == '1 Hour' ? '60' :_htf == '2 Hours' ? '120' :_htf == '3 Hours' ? '180' :_htf == '4 Hours' ? '240' :_htf == '1 Day' ? 'D' :_htf == '1 Week' ? 'W' :_htf == '1 Month' ? 'M' :_htf == '3 Months' ? '3M' :_htf == '6 Months' ? '6M' :_htf == '1 Year' ? '12M' : na

// Global label arrays for each HTF box
var label[] att_candleLabelsHTF1 = array.new_label()
var label att_currentPredictionLabel = na

// Function to check if current bar should show ATT circle
att_f_checkATTCondition(_show, _htf) =>
    result = false
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        result := array.includes(att_att_numbers, currentCandle)
    result

// Function to get ATT number for current bar
att_f_getATTNumber(_show, _htf) =>
    attNumber = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        if array.includes(att_att_numbers, currentCandle)
            attNumber := currentCandle
    attNumber

// Function to get next ATT number and candles remaining
att_f_getNextATT(_show, _htf) =>
    nextATT = 0
    candlesRemaining = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1

        // Find the next ATT number
        for i = 0 to array.size(att_att_numbers) - 1
            attNum = array.get(att_att_numbers, i)
            if attNum > currentCandle
                nextATT := attNum
                candlesRemaining := attNum - currentCandle
                break

        // If no ATT number found in current cycle, get the first one from next cycle
        if nextATT == 0
            nextATT := array.get(att_att_numbers, 0)
            candlesRemaining := (60 - currentCandle) + nextATT

    [nextATT, candlesRemaining]

// Function to check if we should show prediction
att_f_shouldShowPrediction(_show, _htf) =>
    shouldShow = false
    if _show and att_showPrediction
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)
        shouldShow := candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
    shouldShow

// Function to draw candle numbers independently
att_f_drawCandleNumbers(_show, _htf, _labelArr) =>
    if _show and att_showNumbers
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        labelPos = low - (high - low) * 0.5
        candleLabel = label.new(bar_index, labelPos, str.tostring(candleCount + 1), style=label.style_label_center, textcolor=att_numbersColor, size=att_numbersSize, yloc=yloc.price, color=na, textalign=text.align_center)
        array.push(_labelArr, candleLabel)

        // Cleanup labels to prevent overload
        if array.size(_labelArr) > 480
            label.delete(array.shift(_labelArr))

// Function to get prediction data (returns values instead of modifying globals)
att_f_getPredictionData(_show, _htf) =>
    showPrediction = false
    predictionText = ""
    labelColor = att_predictionColor

    if _show and att_showPrediction
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)

        if candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
            showPrediction := true
            predictionText := candlesRemaining == 1 ? str.tostring(nextATT) + " NEXT!" : str.tostring(nextATT) + " in " + str.tostring(candlesRemaining)
            labelColor := candlesRemaining == 1 ? color.orange : att_predictionColor

    [showPrediction, predictionText, labelColor]

att_f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width, _labelArr, _htfNumber) =>
    if _show and att_showBoxes // Only draw boxes if att_showBoxes is true
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)

            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)

            // Cleanup labels to prevent overload
            if array.size(_labelArr) > 480
                label.delete(array.shift(_labelArr))

// ------------------------- Main Logic ------------------------- //

// Process HTF Box 1
att_htf1 = att_f_getTF(att_htfUser1)
att_supported1 = att_checkIf(timeframe.period, att_htf1)

if chart.is_standard
    // Draw HTF Box 1
    if enable_att and att_supported1
        att_f_processCandles(att_htfCndl1, att_htf1, att_bullC1, att_bearC1, att_trans, att_lw, att_candleLabelsHTF1, 1)

// Calculate ATT conditions for HTF timeframe
att_attCondition1 = att_supported1 ? att_f_checkATTCondition(att_htfCndl1, att_htf1) : false
att_attNumber1 = att_supported1 ? att_f_getATTNumber(att_htfCndl1, att_htf1) : 0

// Determine candle color for positioning
att_isRedCandle = close < open
att_isGreenCandle = close >= open

// Calculate if we should show recent ATTs
// Use a simple approach: show if historical is enabled OR if we're within recent bars
att_recentBarsLimit = 120
att_barsFromEnd = last_bar_index - bar_index
att_showRecentATT = att_showHistoricalATT or (att_barsFromEnd <= att_recentBarsLimit)

// Plot ATT circles using plotshape (must be in global scope for historical display)
// HTF1 - With numbers - Red candles (above bar)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-3 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-11 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-17 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-29 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-41 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-47 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-53 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isRedCandle and att_showRecentATT, title="ATT HTF1-59 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - With numbers - Green candles (below bar)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-3 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-11 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-17 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-29 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-41 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-47 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-53 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(enable_att and att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1-59 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - Without numbers
plotshape(enable_att and att_showATT and not att_showATTNumbers and att_attCondition1 and att_isRedCandle and att_showRecentATT, title="ATT HTF1 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small)
plotshape(enable_att and att_showATT and not att_showATTNumbers and att_attCondition1 and att_isGreenCandle and att_showRecentATT, title="ATT HTF1 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small)

// Draw candle numbers for the HTF timeframe
if enable_att and att_supported1 and att_htfCndl1
    att_f_drawCandleNumbers(true, att_htf1, att_candleLabelsHTF1)

// Handle prediction label in global scope
if enable_att and not na(att_currentPredictionLabel)
    label.delete(att_currentPredictionLabel)
    att_currentPredictionLabel := na

if enable_att and att_supported1 and att_htfCndl1
    [showPrediction, predictionText, labelColor] = att_f_getPredictionData(true, att_htf1)
    if showPrediction
        labelPos = (high + low) / 2  // Position at middle of candle
        att_currentPredictionLabel := label.new(bar_index + 1, labelPos, predictionText, style=label.style_label_left, textcolor=color.black, size=att_predictionSize, yloc=yloc.price, color=labelColor, textalign=text.align_left)

//-----att indicator-----



// indicator(title = 'Volume Weighted Colored Bars with Exhaustion & Volatility', overlay = true, max_lines_count = 500)

// -Definitions ════════════════════════════════════════════════════════════════════════════════ //
group_volume_weighted_colored_bars      = 'Volume Weighted Colored Bars'
tooltip_volume_weighted_colored_bars    = 'Colors bars based on the bar\'s volume relative to volume moving average\n' +
                                          'trading tip : a potential breakout trading opportunity may occur when price moves above a resistance level or moves below a support level on increasing volume'

group_volume_spike_sign_of_exhaustion   = 'Volume Spike - Sign of Exhaustion'
tooltip_volume_spike_sign_of_exhaustion = 'Moments where\n' +
                                          'huge volume detected : current volume is grater than the product of the theshold value and volume moving average\n' +
                                          'presents idea : huge volume may be a sign of exhaustion and may lead to sharp reversals'

group_high_volatility                   = 'High Volatility'
tooltip_high_volatility                 = 'Moments where\n' +
                                           'price range of the current bar is grater than the product of the theshold value and average true range value of defined period'

tooltip_volume_moving_average           = 'Volume simple moving average, serves as reference to Volume Weighted Colored Bars calculations'

// -Inputs ════════════════════════════════════════════════════════════════════════════════════ //
// General Settings
i_vSMA_length = input.int(89, 'Volume Moving Average Length', group='General Settings', tooltip=tooltip_volume_moving_average)
srLookbackRange = input.string('Visible Range', 'Lookback Range', options = ['Fixed Range', 'Visible Range'], group='General Settings')
i_lenLookback = input.int(360, 'Fixed Range : Lookback Interval (Bars)', minval=0, step=10, group='General Settings')

// Volume Weighted Colored Bars
i_vwcb = input.bool(true, 'Enable Volume Weighted Colored Bars', inline='VWC', group=group_volume_weighted_colored_bars, tooltip=tooltip_volume_weighted_colored_bars)
i_vwcbHighThresh = input.float(1.618, 'Thresholds : High ', minval=1., step=.1, inline='VWC', group=group_volume_weighted_colored_bars)
i_vwcbLowThresh = input.float(0.618, 'Low', minval=.1, step=.1, inline='VWC', group=group_volume_weighted_colored_bars)

// Volume Spike - Sign of Exhaustion
i_vSpikeLb = input.bool(true, '🚦 Show Volume Spike Indicator', group=group_volume_spike_sign_of_exhaustion, tooltip=tooltip_volume_spike_sign_of_exhaustion)
i_vSpikeThresh = input.float(4.669, 'Volume Spike Threshold', minval=.1, step=.1, group=group_volume_spike_sign_of_exhaustion)

// High Volatility
i_hATRLb = input.bool(true, '⚡ Show High Volatility Indicator', group=group_high_volatility, tooltip=tooltip_high_volatility)
i_atrLength = input.int(11, 'ATR Length', group=group_high_volatility)
i_atrMult = input.float(2.718, 'ATR Multiplier', minval=.1, step=.1, group=group_high_volatility)

// -Calculations ════════════════════════════════════════════════════════════════════════════════ //
nzVolume = nz(volume)
i_vSMA = ta.sma(nzVolume, i_vSMA_length)

bullCandle = close > open
bearCandle = close < open

risingPrice  = close > close[1]
fallingPrice = close < close[1]

lwstPrice = ta.lowest(low, 3)
hstPrice  = ta.highest(high, 3)

weightedATR = i_atrMult * ta.atr(i_atrLength)
range_1 = math.abs(high - low)

x2 = timenow + 7 * math.round(ta.change(time))

var sProcessing = false
if srLookbackRange == 'Visible Range'
    sProcessing := time >= chart.left_visible_bar_time
else
    sProcessing := time > timenow - i_lenLookback * (timeframe.isintraday ? timeframe.multiplier * 86400000 / 1440 : timeframe.multiplier * 86400000)

// Volume Spike - Sign of Exhaustion
exhaustVol = nzVolume > i_vSpikeThresh * i_vSMA
x1V = ta.valuewhen(exhaustVol, time, 0)

// High Volatility
highVolatility = range_1 > weightedATR
x1hV = ta.valuewhen(highVolatility, time, 0)

// -Plotting ════════════════════════════════════════════════════════════════════════════════════ //

// Volume Weighted Colored Bars
vwcbCol = nzVolume > i_vSMA * i_vwcbHighThresh ?bearCandle ? color.rgb(153, 6, 6) : #056205 :nzVolume < i_vSMA * i_vwcbLowThresh ?  bearCandle ? #FF9800 : #7FFFD4 :na

barcolor(i_vwcb and not na(nzVolume) ? vwcbCol : na, title='Volume Weighted Colored Bars')

// Volume Spike - Sign of Exhaustion

plotchar(i_vSpikeLb and not na(nzVolume) and sProcessing ? exhaustVol : na, 'Exhaustion Bar', '🚦', location.abovebar, size=size.tiny)

// High Volatility
plotchar(i_hATRLb and sProcessing ? highVolatility : na, 'High Volatile Bar', '⚡', location.belowbar, size=size.tiny)

// -Alerts ════════════════════════════════════════════════════════════════════════════════════ //
priceTxt = str.tostring(close, format.mintick)
tickerTxt = syminfo.ticker

if nzVolume > i_vSMA * i_vwcbHighThresh and i_vwcb
    alert(tickerTxt + ' High Volume, price ' + priceTxt)

if nzVolume > i_vSMA * i_vSpikeThresh and i_vSpikeLb
    alert(tickerTxt + ' Volume Spike : sign of exhaustion, huge volume increase detected, price ' + priceTxt)

if ta.crossover(range_1, weightedATR) and i_hATRLb
    alert(tickerTxt + ' High Volatility detected, price ' + priceTxt)