// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Dev Lucem

//@version=5
indicator('Impulse vs Corrective Move Analyzer', 'ICA [LD]', true, format.price, max_labels_count=500, max_lines_count=200, max_boxes_count=100)

// Import ZigZag Library
import DevLucem/ZigLib/1 as ZigZag

//==============================================================================
// INPUT SETTINGS
//==============================================================================

// ZigZag Settings
var GRP_ZZ = "ZigZag Settings"
Depth = input.int(12, 'Depth', minval=1, step=1, group=GRP_ZZ)
Deviation = input.int(5, 'Deviation', minval=1, step=1, group=GRP_ZZ)
Backstep = input.int(2, 'Backstep', minval=2, step=1, group=GRP_ZZ)

// Volume Analysis Settings
var GRP_VOL = "Volume Analysis"
volSmaLength = input.int(20, 'Volume SMA Length', minval=1, group=GRP_VOL)
volMultiplier = input.float(1.5, 'High Volume Multiplier', minval=0.1, step=0.1, group=GRP_VOL)
exhaustionMultiplier = input.float(3.0, 'Exhaustion Volume Multiplier', minval=1.0, step=0.1, group=GRP_VOL)

// Speed Analysis Settings
var GRP_SPEED = "Speed Analysis"
speedLookback = input.int(10, 'Speed Calculation Period', minval=5, maxval=50, group=GRP_SPEED)
atrLength = input.int(14, 'ATR Length for Speed', minval=1, group=GRP_SPEED)
speedThreshold = input.float(1.5, 'High Speed Threshold (ATR Multiple)', minval=0.5, step=0.1, group=GRP_SPEED)

// Classification Settings
var GRP_CLASS = "Classification Settings"
impulseVolThreshold = input.float(1.8, 'Impulse Volume Threshold', minval=1.0, step=0.1, group=GRP_CLASS)
impulseSpeedThreshold = input.float(1.2, 'Impulse Speed Threshold', minval=0.5, step=0.1, group=GRP_CLASS)
correctiveVolThreshold = input.float(0.8, 'Corrective Volume Threshold', minval=0.1, step=0.1, group=GRP_CLASS)
correctiveSpeedThreshold = input.float(0.6, 'Corrective Speed Threshold', minval=0.1, step=0.1, group=GRP_CLASS)

// Display Settings
var GRP_DISPLAY = "Display Settings"
showImpulseLabels = input.bool(true, 'Show Impulse Labels', group=GRP_DISPLAY)
showCorrectiveLabels = input.bool(true, 'Show Corrective Labels', group=GRP_DISPLAY)
showVolumeColors = input.bool(true, 'Show Volume Colored Bars', group=GRP_DISPLAY)
showSpeedIndicators = input.bool(true, 'Show Speed Indicators', group=GRP_DISPLAY)
impulseColor = input.color(color.new(color.lime, 0), 'Impulse Color', group=GRP_DISPLAY)
correctiveColor = input.color(color.new(color.orange, 0), 'Corrective Color', group=GRP_DISPLAY)
highVolColor = input.color(color.new(color.red, 0), 'High Volume Color', group=GRP_DISPLAY)
lowVolColor = input.color(color.new(color.gray, 50), 'Low Volume Color', group=GRP_DISPLAY)

//==============================================================================
// CALCULATIONS
//==============================================================================

// ZigZag Calculation
[direction, z1, z2] = ZigZag.zigzag(low, high, Depth, Deviation, Backstep)

// Volume Analysis
volSma = ta.sma(volume, volSmaLength)
relativeVolume = volume / volSma
isHighVolume = relativeVolume > volMultiplier
isExhaustionVolume = relativeVolume > exhaustionMultiplier

// Speed Analysis (Price movement per bar)
atr = ta.atr(atrLength)
priceChange = math.abs(close - close[1])
relativeSpeed = priceChange / atr
isHighSpeed = relativeSpeed > speedThreshold

// ZigZag Swing Analysis
var float lastSwingPrice = na
var int lastSwingBar = na
float swingDistance = na
int swingDuration = na
float avgVolume = na
float avgSpeed = na
bool isImpulsive = na

// Detect new swing completion
if ta.change(direction) != 0
    if not na(lastSwingPrice) and not na(lastSwingBar)
        // Calculate swing metrics
        swingDistance := math.abs(z1.price[1] - lastSwingPrice)
        swingDuration := bar_index - lastSwingBar
        
        // Calculate average volume and speed during the swing
        float volSum = 0.0
        float speedSum = 0.0
        int validBars = 0
        
        for i = 0 to swingDuration - 1
            if bar_index - i >= 0
                volSum := volSum + (volume[i] / volSma[i])
                speedSum := speedSum + (math.abs(close[i] - close[i+1]) / atr[i])
                validBars := validBars + 1
        
        avgVolume := validBars > 0 ? volSum / validBars : 0
        avgSpeed := validBars > 0 ? speedSum / validBars : 0
        
        // Classify the move
        isImpulsive := (avgVolume > impulseVolThreshold and avgSpeed > impulseSpeedThreshold) or
                       (avgVolume > impulseVolThreshold * 1.5) or 
                       (avgSpeed > impulseSpeedThreshold * 1.5)
    
    // Update for next swing
    lastSwingPrice := z1.price[1]
    lastSwingBar := bar_index

//==============================================================================
// MOVE CLASSIFICATION LOGIC
//==============================================================================

// Enhanced classification based on multiple factors
string moveType = na
color moveColor = na

if not na(isImpulsive) and ta.change(direction) != 0
    bool isUpMove = direction[1] > 0
    
    if isImpulsive
        moveType := isUpMove ? "IMPULSE ↑" : "IMPULSE ↓"
        moveColor := impulseColor
    else
        // Check if it's corrective
        bool isCorrective = (avgVolume < correctiveVolThreshold and avgSpeed < correctiveSpeedThreshold) or
                           (avgVolume < correctiveVolThreshold * 1.2 and avgSpeed < correctiveSpeedThreshold * 1.2)
        
        if isCorrective
            moveType := isUpMove ? "CORRECTIVE ↑" : "CORRECTIVE ↓"
            moveColor := correctiveColor
        else
            moveType := isUpMove ? "NEUTRAL ↑" : "NEUTRAL ↓"
            moveColor := color.gray

//==============================================================================
// VOLUME WEIGHTED COLORED BARS
//==============================================================================

// Volume bar coloring logic
color volBarColor = na
if showVolumeColors
    if isHighVolume
        volBarColor := close > open ? color.new(color.green, 0) : color.new(color.red, 0)
    else if relativeVolume < correctiveVolThreshold
        volBarColor := lowVolColor
    else
        volBarColor := na

barcolor(volBarColor, title='Volume Weighted Bars')

//==============================================================================
// PLOTTING
//==============================================================================

// ZigZag Lines
line zzLine = na
if direction != direction[1] and not na(z1.price[1]) and not na(z2.price[1])
    zzLine := line.new(z1[1], z2[1], xloc.bar_time, extend.none, 
                       direction[1] > 0 ? impulseColor : correctiveColor, width=2)

// Move Classification Labels
if not na(moveType) and ta.change(direction) != 0
    labelStyle = direction[1] > 0 ? label.style_label_down : label.style_label_up
    
    if (showImpulseLabels and str.contains(moveType, "IMPULSE")) or 
       (showCorrectiveLabels and str.contains(moveType, "CORRECTIVE"))
        label.new(z1[1], moveType + "\nVol: " + str.tostring(avgVolume, "#.##") + 
                  "\nSpd: " + str.tostring(avgSpeed, "#.##"), 
                  xloc.bar_time, yloc.price, moveColor, labelStyle, 
                  color.white, size.normal)

// Speed Indicators
if showSpeedIndicators
    plotchar(isHighSpeed, 'High Speed', '⚡', location.belowbar, 
             color.new(color.yellow, 0), size=size.tiny)

// Volume Exhaustion Indicators  
plotchar(isExhaustionVolume, 'Volume Exhaustion', '🚦', location.abovebar, 
         color.new(color.red, 0), size=size.tiny)

//==============================================================================
// TABLE DISPLAY
//==============================================================================

// Summary table
var table infoTable = table.new(position.top_right, 3, 6, bgcolor=color.new(color.white, 80), border_width=1, border_color=color.gray)

if barstate.islast
    table.cell(infoTable, 0, 0, "Metric", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 0, "Current", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 2, 0, "Threshold", text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 1, "Rel Volume", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 1, str.tostring(relativeVolume, "#.##"), 
               text_color=isHighVolume ? color.red : color.green, text_size=size.small)
    table.cell(infoTable, 2, 1, str.tostring(volMultiplier, "#.##"), 
               text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 2, "Rel Speed", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 2, str.tostring(relativeSpeed, "#.##"), 
               text_color=isHighSpeed ? color.red : color.green, text_size=size.small)
    table.cell(infoTable, 2, 2, str.tostring(speedThreshold, "#.##"), 
               text_color=color.black, text_size=size.small)
    
    if not na(moveType)
        table.cell(infoTable, 0, 3, "Last Move", text_color=color.black, text_size=size.small)
        table.cell(infoTable, 1, 3, moveType, text_color=moveColor, text_size=size.small)
        table.cell(infoTable, 2, 3, "", text_color=color.black, text_size=size.small)
        
        table.cell(infoTable, 0, 4, "Avg Vol", text_color=color.black, text_size=size.small)
        table.cell(infoTable, 1, 4, str.tostring(avgVolume, "#.##"), 
                   text_color=color.black, text_size=size.small)
        table.cell(infoTable, 2, 4, "", text_color=color.black, text_size=size.small)
        
        table.cell(infoTable, 0, 5, "Avg Speed", text_color=color.black, text_size=size.small)
        table.cell(infoTable, 1, 5, str.tostring(avgSpeed, "#.##"), 
                   text_color=color.black, text_size=size.small)
        table.cell(infoTable, 2, 5, "", text_color=color.black, text_size=size.small)

//==============================================================================
// ALERTS
//==============================================================================

alertcondition(not na(moveType) and str.contains(moveType, "IMPULSE"), 
               title="Impulse Move Detected", 
               message="Impulse move detected: {{ticker}} - " + moveType)

alertcondition(not na(moveType) and str.contains(moveType, "CORRECTIVE"), 
               title="Corrective Move Detected", 
               message="Corrective move detected: {{ticker}} - " + moveType)

alertcondition(isExhaustionVolume, 
               title="Volume Exhaustion", 
               message="Volume exhaustion detected: {{ticker}} - Volume: " + str.tostring(relativeVolume, "#.##"))
